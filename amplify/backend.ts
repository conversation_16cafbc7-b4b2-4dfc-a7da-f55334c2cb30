import { defineBackend } from "@aws-amplify/backend";
import { auth } from "./auth/resource";
import { data } from "./data/resource";
import { chatController } from "./functions/src/functions/chatController/resource";
import { userController } from "./functions/src/functions/userController/resource";
import { gatewayController } from "./functions/src/functions/gatewayController/resource";
import { caseController } from "./functions/src/functions/caseController/resource";
import { maisController } from "./functions/src/functions/maisController/resource";
import { authController } from "./functions/src/functions/authController/resource";
import { otpController } from "./functions/src/functions/otpController/resource";
import { connectSocketController } from "./functions/src/functions/chatSocket/connectController/resource";
import { disconnectSocketController } from "./functions/src/functions/chatSocket/disconnectController/resource";
import { inboundSocketController } from "./functions/src/functions/chatSocket/inboundController/resource";
import { outboundSocketController } from "./functions/src/functions/chatSocket/outboundController/resource";
import { Stack } from "aws-cdk-lib";
import {
  AuthorizationType,
  Cors,
  LambdaIntegration,
  RestApi,
} from "aws-cdk-lib/aws-apigateway";
import { Effect, Policy, PolicyStatement } from "aws-cdk-lib/aws-iam";
import { EventSourceMapping, StartingPosition } from "aws-cdk-lib/aws-lambda";
import { WebSocketApi, WebSocketStage } from "aws-cdk-lib/aws-apigatewayv2";
import { WebSocketLambdaIntegration } from "aws-cdk-lib/aws-apigatewayv2-integrations";

/**
 * @see https://docs.amplify.aws/react/build-a-backend/ to add storage, functions, and more
 */
const backend = defineBackend({
  auth,
  data,
  chatController,
  userController,
  gatewayController,
  maisController,
  caseController,
  authController,
  otpController,
  connectSocketController,
  disconnectSocketController,
  inboundSocketController,
  outboundSocketController,
});

const apiStack = backend.createStack("monova-api-stack");

const wsApi = new WebSocketApi(apiStack, "ChatSocketApi_TEST_ANKUR", {
  apiName: "chatSocketApi_TEST_ANKUR",
  routeSelectionExpression: "$request.body.action",
});

// wsApi.grantManageConnections(backend.connectSocketController.resources.lambda);
// wsApi.grantManageConnections(backend.disconnectSocketController.resources.lambda);
// wsApi.grantManageConnections(backend.inboundSocketController.resources.lambda);

const wsStage = new WebSocketStage(apiStack, "chatSocketStage_TEST_ANKUR", {
  webSocketApi: wsApi,
  stageName: "dev",
  autoDeploy: true,
});

// Websocket integrations
const chatConnectIntegration = new WebSocketLambdaIntegration(
  "chatConnectIntegration",
  backend.connectSocketController.resources.lambda
);

const chatDisconnectIntegration = new WebSocketLambdaIntegration(
  "chatDisconnectIntegration",
  backend.disconnectSocketController.resources.lambda
);

const chatInboundIntegration = new WebSocketLambdaIntegration(
  "chatInboundIntegration",
  backend.inboundSocketController.resources.lambda
);

wsApi.addRoute("$connect", {
  integration: chatConnectIntegration,
});

wsApi.addRoute("$disconnect", {
  integration: chatDisconnectIntegration,
});

wsApi.addRoute("inbound", {
  integration: chatInboundIntegration,
});

const wsPolicy = new Policy(apiStack, "ChatSocketPolicy_TEST_ANKUR", {
  statements: [
    new PolicyStatement({
      effect: Effect.ALLOW,
      actions: [
        "execute-api:Invoke",
        "execute-api:ManageConnections",
        "apigateway:*",
      ],
      resources: [`*`],
    }),
  ],
});

backend.auth.resources.authenticatedUserIamRole.attachInlinePolicy(wsPolicy);
backend.auth.resources.unauthenticatedUserIamRole.attachInlinePolicy(wsPolicy);

const outboundMessageApi = new RestApi(
  apiStack,
  "OutboundMessageApi_TEST_ANKUR",
  {
    restApiName: "outboundMessageApi_TEST_ANKUR",
    deploy: true,
    deployOptions: {
      stageName: "dev",
    },
    defaultCorsPreflightOptions: {
      allowOrigins: Cors.ALL_ORIGINS,
      allowMethods: Cors.ALL_METHODS,
      allowHeaders: Cors.DEFAULT_HEADERS,
    },
  }
);

const outboundMessageLambdaIntegration = new LambdaIntegration(
  backend.outboundSocketController.resources.lambda
);

const outboundMessageDefaultPath = outboundMessageApi.root.addResource(
  "outbound",
  {
    defaultMethodOptions: {
      authorizationType: AuthorizationType.NONE,
    },
  }
);

outboundMessageDefaultPath.addMethod("POST", outboundMessageLambdaIntegration);

outboundMessageDefaultPath.addProxy({
  anyMethod: true,
  defaultIntegration: outboundMessageLambdaIntegration,
});

const outboundMessagePolicy = new Policy(
  apiStack,
  "OutboundMessagePolicy_TEST_ANKUR",
  {
    statements: [
      new PolicyStatement({
        effect: Effect.ALLOW,
        actions: [
          "execute-api:Invoke",
          "execute-api:ManageConnections",
          "apigateway:*",
        ],
        resources: [`*`],
      }),
    ],
  }
);

backend.outboundSocketController.resources.lambda.role?.attachInlinePolicy(
  outboundMessagePolicy
);

const authApi = new RestApi(apiStack, "AuthApi_TEST_ANKUR", {
  restApiName: "authApi_TEST_ANKUR",
  deploy: true,
  deployOptions: {
    stageName: "dev",
  },
  defaultCorsPreflightOptions: {
    allowOrigins: Cors.ALL_ORIGINS, // Restrict this to domains you trust
    allowMethods: Cors.ALL_METHODS, // Specify only the methods you need to allow
    allowHeaders: Cors.DEFAULT_HEADERS, // Specify only the headers you need to allow
  },
});

const authLambdaIntegration = new LambdaIntegration(
  backend.authController.resources.lambda
);

const authDefaultPath = authApi.root.addResource("auth", {
  defaultMethodOptions: {
    authorizationType: AuthorizationType.NONE,
  },
});

authDefaultPath.addProxy({
  anyMethod: true,
  defaultIntegration: authLambdaIntegration,
});

authDefaultPath.addMethod("POST", authLambdaIntegration);

const otpRecord = backend.data.resources.tables["otpRecord"];
const DDBStreamPolicy = new Policy(
  Stack.of(otpRecord),
  "DDBStreamPolicy_TEST_ANKUR",
  {
    statements: [
      new PolicyStatement({
        effect: Effect.ALLOW,
        actions: [
          "dynamodb:DescribeStream",
          "dynamodb:GetRecords",
          "dynamodb:GetShardIterator",
          "dynamodb:ListStreams",
          // NOTE : For future we might use SNS
          "sns:Publish",
        ],
        resources: ["*"],
      }),
    ],
  }
);

const streamMapping = new EventSourceMapping(
  Stack.of(otpRecord),
  "OtpStreamMapping_TEST_ANKUR",
  {
    target: backend.otpController.resources.lambda,
    eventSourceArn: otpRecord.tableStreamArn,
    startingPosition: StartingPosition.LATEST,
  }
);

backend.otpController.resources.lambda.role?.attachInlinePolicy(
  DDBStreamPolicy
);

streamMapping.node.addDependency(DDBStreamPolicy);
//---------------------------------<chat>---------------------------------//

const chatApi = new RestApi(apiStack, "ChatApi_TEST_ANKUR", {
  restApiName: "chatApi_TEST_ANKUR",
  deploy: true,
  deployOptions: {
    stageName: "dev",
  },
  defaultCorsPreflightOptions: {
    allowOrigins: Cors.ALL_ORIGINS, // Restrict this to domains you trust
    allowMethods: Cors.ALL_METHODS, // Specify only the methods you need to allow
    allowHeaders: Cors.DEFAULT_HEADERS, // Specify only the headers you need to allow
  },
});

const chatLambdaIntegration = new LambdaIntegration(
  backend.chatController.resources.lambda
);

const chatDefaultPath = chatApi.root.addResource("chat", {
  defaultMethodOptions: {
    authorizationType: AuthorizationType.NONE,
  },
});

chatDefaultPath.addMethod("GET", chatLambdaIntegration);
chatDefaultPath.addMethod("POST", chatLambdaIntegration);
chatDefaultPath.addMethod("DELETE", chatLambdaIntegration);
chatDefaultPath.addMethod("PUT", chatLambdaIntegration);

chatDefaultPath.addProxy({
  anyMethod: true,
  defaultIntegration: chatLambdaIntegration,
});
//---------------------------------<user>---------------------------------//

const userApi = new RestApi(apiStack, "UserApi_TEST_ANKUR", {
  restApiName: "userApi_TEST_ANKUR",
  deploy: true,
  deployOptions: {
    stageName: "dev",
  },
  defaultCorsPreflightOptions: {
    allowOrigins: Cors.ALL_ORIGINS, // Restrict this to domains you trust
    allowMethods: Cors.ALL_METHODS, // Specify only the methods you need to allow
    allowHeaders: Cors.DEFAULT_HEADERS, // Specify only the headers you need to allow
  },
});

const userLambdaIntegration = new LambdaIntegration(
  backend.userController.resources.lambda
);

const userDefaultPath = userApi.root.addResource("user", {
  defaultMethodOptions: {
    authorizationType: AuthorizationType.NONE,
  },
});

userDefaultPath.addMethod("GET", userLambdaIntegration);
userDefaultPath.addMethod("POST", userLambdaIntegration);
userDefaultPath.addMethod("DELETE", userLambdaIntegration);
userDefaultPath.addMethod("PUT", userLambdaIntegration);

userDefaultPath.addProxy({
  anyMethod: true,
  defaultIntegration: userLambdaIntegration,
});

//---------------------------------<gateway>---------------------------------//
const gatewayApi = new RestApi(apiStack, "GatewayApi_TEST_ANKUR", {
  restApiName: "gatewayApi_TEST_ANKUR",
  deploy: true,
  deployOptions: {
    stageName: "dev",
  },
  defaultCorsPreflightOptions: {
    allowOrigins: Cors.ALL_ORIGINS, // Restrict this to domains you trust
    allowMethods: Cors.ALL_METHODS, // Specify only the methods you need to allow
    allowHeaders: Cors.DEFAULT_HEADERS, // Specify only the headers you need to allow
  },
});

const gatewayLambdaIntegration = new LambdaIntegration(
  backend.gatewayController.resources.lambda
);

const gatewayDefaultPath = gatewayApi.root.addResource("gateway", {
  defaultMethodOptions: {
    authorizationType: AuthorizationType.NONE,
  },
});

gatewayDefaultPath.addMethod("GET", gatewayLambdaIntegration);
gatewayDefaultPath.addMethod("POST", gatewayLambdaIntegration);
gatewayDefaultPath.addMethod("DELETE", gatewayLambdaIntegration);
gatewayDefaultPath.addMethod("PUT", gatewayLambdaIntegration);

gatewayDefaultPath.addProxy({
  anyMethod: true,
  defaultIntegration: gatewayLambdaIntegration,
});

const caseApi = new RestApi(apiStack, "CaseApi_TEST_ANKUR", {
  restApiName: "caseApi_TEST_ANKUR",
  deploy: true,
  deployOptions: {
    stageName: "dev",
  },
  defaultCorsPreflightOptions: {
    allowOrigins: Cors.ALL_ORIGINS, // Restrict this to domains you trust
    allowMethods: Cors.ALL_METHODS, // Specify only the methods you need to allow
    allowHeaders: Cors.DEFAULT_HEADERS, // Specify only the headers you need to allow
  },
});

const caseLambdaIntegration = new LambdaIntegration(
  backend.caseController.resources.lambda
);

const caseDefaultPath = caseApi.root.addResource("case", {
  defaultMethodOptions: {
    authorizationType: AuthorizationType.NONE,
  },
});

caseDefaultPath.addMethod("GET", caseLambdaIntegration);
caseDefaultPath.addMethod("POST", caseLambdaIntegration);
caseDefaultPath.addMethod("DELETE", caseLambdaIntegration);
caseDefaultPath.addMethod("PUT", caseLambdaIntegration);

caseDefaultPath.addProxy({
  anyMethod: true,
  defaultIntegration: caseLambdaIntegration,
});

const maisApi = new RestApi(apiStack, "MAISApi_TEST_ANKUR", {
  restApiName: "maisApi_TEST_ANKUR",
  deploy: true,
  deployOptions: {
    stageName: "dev",
  },
  defaultCorsPreflightOptions: {
    allowOrigins: Cors.ALL_ORIGINS, // Restrict this to domains you trust
    allowMethods: Cors.ALL_METHODS, // Specify only the methods you need to allow
    allowHeaders: Cors.DEFAULT_HEADERS, // Specify only the headers you need to allow
  },
});

const maisLambdaIntegration = new LambdaIntegration(
  backend.maisController.resources.lambda
);

const maisDefaultPath = maisApi.root.addResource("mais", {
  defaultMethodOptions: {
    authorizationType: AuthorizationType.NONE,
  },
});

maisDefaultPath.addMethod("GET", maisLambdaIntegration);
maisDefaultPath.addMethod("POST", maisLambdaIntegration);
maisDefaultPath.addMethod("DELETE", maisLambdaIntegration);
maisDefaultPath.addMethod("PUT", maisLambdaIntegration);

maisDefaultPath.addProxy({
  anyMethod: true,
  defaultIntegration: maisLambdaIntegration,
});

// const cognitoAuth = new CognitoUserPoolsAuthorizer(apiStack, "CognitoAuth", {
//   cognitoUserPools: [backend.auth.resources.userPool],
// });

// // create a new resource path with Cognito authorization
// const booksPath = myRestApi.root.addResource("cognito-auth-path");
// booksPath.addMethod("GET", lambdaIntegration, {
//   authorizationType: AuthorizationType.COGNITO,
//   authorizer: cognitoAuth,
// });

// create a new IAM policy to allow Invoke access to the API
const apiRestPolicy = new Policy(apiStack, "RestApiPolicy", {
  statements: [
    new PolicyStatement({
      actions: [
        "execute-api:Invoke",
        "execute-api:ManageConnections",
        "dynamodb:PutItem",
        "dynamodb:GetItem",
        "dynamodb:Query",
        "dynamodb:UpdateItem",
        "dynamodb:DeleteItem",
        "dynamodb:BatchGetItem",
        "dynamodb:BatchWriteItem",
      ],
      resources: [
        `${chatApi.arnForExecuteApi("*", "/", "dev")}`,
        `${chatApi.arnForExecuteApi("*", "/chat/*", "dev")}`,
        // `${chatApi.arnForExecuteApi("*", "/cognito-auth-path", "dev")}`,
        `${userApi.arnForExecuteApi("*", "/", "dev")}`,
        `${userApi.arnForExecuteApi("*", "/user/*", "dev")}`,
        `${gatewayApi.arnForExecuteApi("*", "/", "dev")}`,
        `${gatewayApi.arnForExecuteApi("*", "/gateway/*", "dev")}`,
        `${caseApi.arnForExecuteApi("*", "/", "dev")}`,
        `${caseApi.arnForExecuteApi("*", "/case/*", "dev")}`,
        `${maisApi.arnForExecuteApi("*", "/", "dev")}`,
        `${maisApi.arnForExecuteApi("*", "/mais/*", "dev")}`,
        `${authApi.arnForExecuteApi("*", "/", "dev")}`,
        `${authApi.arnForExecuteApi("*", "/auth/*", "dev")}`,
        `${outboundMessageApi.arnForExecuteApi("*", "/", "dev")}`,
        `${outboundMessageApi.arnForExecuteApi("*", "/outbound/*", "dev")}`,
        `arn:aws:execute-api:${Stack.of(wsApi).region}:${
          Stack.of(wsApi).account
        }:${wsApi.apiId}/*/*/@connections/*`,
        `arn:aws:dynamodb:${Stack.of(apiStack).region}:${
          Stack.of(apiStack).account
        }:table/messages_monova_test`,
        `arn:aws:dynamodb:${Stack.of(apiStack).region}:${
          Stack.of(apiStack).account
        }:table/messages_monova_test/index/*`,
      ],
    }),
  ],
});

// attach the policy to the authenticated and unauthenticated IAM roles
backend.auth.resources.authenticatedUserIamRole.attachInlinePolicy(
  apiRestPolicy
);
backend.auth.resources.unauthenticatedUserIamRole.attachInlinePolicy(
  apiRestPolicy
);

const dynamoDBPolicy = new PolicyStatement({
  effect: Effect.ALLOW,
  actions: [
    "dynamodb:PutItem",
    "dynamodb:GetItem",
    "dynamodb:Query", // This was missing and causing the error
    "dynamodb:UpdateItem",
    "dynamodb:DeleteItem",
    "dynamodb:BatchGetItem",
    "dynamodb:BatchWriteItem",
    "dynamodb:Scan",
  ],
  resources: [
    `arn:aws:dynamodb:${Stack.of(apiStack).region}:${
      Stack.of(apiStack).account
    }:table/messages_monova_test`,
    `arn:aws:dynamodb:${Stack.of(apiStack).region}:${
      Stack.of(apiStack).account
    }:table/messages_monova_test/index/*`,
    `arn:aws:dynamodb:${Stack.of(apiStack).region}:${
      Stack.of(apiStack).account
    }:table/cases_monova_test`,
    `arn:aws:dynamodb:${Stack.of(apiStack).region}:${
      Stack.of(apiStack).account
    }:table/cases_monova_test/index/*`,
  ],
});

// Add the policy to all controller Lambdas
backend.maisController.resources.lambda.addToRolePolicy(dynamoDBPolicy);
backend.chatController.resources.lambda.addToRolePolicy(dynamoDBPolicy);
backend.caseController.resources.lambda.addToRolePolicy(dynamoDBPolicy);
backend.gatewayController.resources.lambda.addToRolePolicy(dynamoDBPolicy);

// add outputs to the configuration file
backend.addOutput({
  custom: {
    API: {
      [chatApi.restApiName]: {
        endpoint: chatApi.url,
        region: Stack.of(chatApi).region,
        apiName: chatApi.restApiName,
      },
      [userApi.restApiName]: {
        endpoint: userApi.url,
        region: Stack.of(userApi).region,
        apiName: userApi.restApiName,
      },
      [gatewayApi.restApiName]: {
        endpoint: gatewayApi.url,
        region: Stack.of(gatewayApi).region,
        apiName: gatewayApi.restApiName,
      },
      [caseApi.restApiName]: {
        endpoint: caseApi.url,
        region: Stack.of(caseApi).region,
        apiName: caseApi.restApiName,
      },
      [maisApi.restApiName]: {
        endpoint: maisApi.url,
        region: Stack.of(maisApi).region,
        apiName: maisApi.restApiName,
      },
      [authApi.restApiName]: {
        endpoint: authApi.url,
        region: Stack.of(authApi).region,
        apiName: authApi.restApiName,
      },
      [outboundMessageApi.restApiName]: {
        endpoint: outboundMessageApi.url,
        region: Stack.of(outboundMessageApi).region,
        apiName: outboundMessageApi.restApiName,
      },

      [wsApi.webSocketApiName as string]: {
        domain: wsApi.apiEndpoint,
        region: Stack.of(wsApi).region,
        apiName: wsApi.webSocketApiName,
        stage: wsStage.stageName,
      },
    },
  },
});
