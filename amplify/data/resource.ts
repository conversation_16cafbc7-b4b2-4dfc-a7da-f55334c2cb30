import { type ClientSchema, a, defineData } from "@aws-amplify/backend";

const schema = a.schema({
  UserInfo: a
    .model({
      userId: a.string().required(),
      userFullName: a.string().required(),
      userWhatsAppNumber: a.string().required(),
      userStatus: a.enum(["NEW", "ONBOARDED", "INACTIVE"]),
      userRole: a.enum(["USER", "ADMIN", "GUEST"]),
      token: a.string().required(),
    })
    .identifier(["userId"])
    .authorization((allow) => [allow.guest()]),
  otpRecord: a
    .model({
      userId: a.string().required(),
      otp: a.string().required(),
      expiresAt: a.integer().required(),
    })
    .identifier(["userId"])
    .authorization((allow) => [allow.guest()]),
  SocketConnection: a
    .model({
      userId: a.string().required(),
      connectionId: a.string().required(),
      createdAt: a.string().required(),
    })
    .authorization((allow) => [allow.guest()])
    .identifier(["userId"]),
  UserStyleProfile: a
    .model({
      userId: a.string().required(),
      userGender: a.enum(["MALE", "FEMALE"]),
      userBodyType: a.enum([
        "RECTANGLE",
        "INVERTED_TRIANGLE",
        "TRIANGLE",
        "HOURGLASS",
        "PEAR",
        "OVAL",
        "TRAPEZOID",
      ]),
      userSkinUnderTone: a.enum(["WARM", "NEUTRAL", "COOL", "TANNED", "DARK"]),
      userAge: a.enum([
        "AGE_0_20",
        "AGE_21_24",
        "AGE_25_30",
        "AGE_31_36",
        "AGE_36_99",
      ]),
    })
    .identifier(["userId"])
    .authorization((allow) => [allow.guest()]),
});

export type Schema = ClientSchema<typeof schema>;

export const data = defineData({
  schema,
  authorizationModes: {
    defaultAuthorizationMode: "iam",
  },
});
