import { AxiosClient } from "../config/axiosClient";

interface ChatMessage {
  role: "system" | "user" | "assistant" | "chatHistory" | "newUserMessage";
  content: string | string[];
}

interface AzureAIConfig {
  apiVersion: string;
  deploymentId: string;
  endpoint: string;
  apiKey: string;
}

export class AIService {
  private config: AzureAIConfig;
  private aiClient;
  private dalleClient;

  constructor() {
    // Load configuration from environment variables
    this.config = {
      apiVersion: process.env.AZURE_OPENAI_API_VERSION || "2024-06-01",
      deploymentId: process.env.AZURE_OPENAI_DEPLOYMENT_ID || "",
      endpoint: process.env.AZURE_OPENAI_ENDPOINT || "",
      apiKey:
        process.env.AZURE_OPENAI_API_KEY || "********************************",
    };
    this.aiClient = AxiosClient.getInstance(
      "https://whatsapp-ai-stylist-monova.openai.azure.com/openai/deployments"
    ).getClient();
    this.dalleClient = AxiosClient.getInstance(
      "https://whatsapp-ai-stylist-monova.openai.azure.com/openai/deployments/dall-e-3"
    ).getClient();
  }

  async getAIResponse(
    userPrompt: string[],
    systemPrompt: string,
    model: string = "gpt-4o-mini-2",
    messageId?: string
  ): Promise<string> {
    try {
      const messages: ChatMessage[] = [
        {
          role: "system",
          content: systemPrompt,
        },
      ];

      userPrompt.forEach(prompt => {
        messages.push({
            role: "user",
            content: prompt
        });
    });

      console.log(
        `message tid=${messageId} being sent to chatgpt-> ${JSON.stringify(
          messages
        )}`
      );

      const url = `/${model}/chat/completions?api-version=${this.config.apiVersion}`;
      // /gpt-40-mini/chat/completions?api-version=2024-06-01

      const response = await this.aiClient.post(
        url,
        {
          messages,
          model,
          temperature: 0.7,
          max_tokens: 800,
          top_p: 0.95,
          frequency_penalty: 0,
          presence_penalty: 0,
          stop: null,
        },
        {
          headers: {
            "api-key": this.config.apiKey,
            "Content-Type": "application/json",
          },
        }
      );

      if (!response.data.choices || response.data.choices.length === 0) {
        throw new Error("No response from AI service");
      }

      console.log(
        `[AIIntegrationService] tid=${messageId} response received from AI call -> ${JSON.stringify(
          response.data.choices[0].message.content,
          null,
          2
        )}`
      );

      return response.data.choices[0].message.content;
    } catch (error) {
      console.error("Error calling Azure OpenAI:", error);
      throw error
    }
  }
  async generateImage(prompt: string): Promise<string> {
    const response = await this.dalleClient.post(
      "/images/generations?api-version=2024-06-01",
      {
        prompt,
        // size: "1024x1024",
        n: 1,
        quality: "standard",
        style: "vivid",
      },
      {
        headers: {
          "api-key": this.config.apiKey,
          "Content-Type": "application/json",
        },
      }
    );

    return response.data.data[0].url;
  }
  async getImageAnalysis(
    imageUrl: string, 
    userPrompt: string, 
    systemPrompt: string,
    model: string = "gpt-4o-mini-2"
  ): Promise<string> {
    try {
      const messages = [
        {
          role: "system",
          content: systemPrompt,
        },
        {
          role: "user",
          content: [
            { type: "text", text: userPrompt },
            {
              type: "image_url",
              image_url: {
                url: imageUrl
              }
            }
          ]
        }
      ];
  
      const url = `/${model}/chat/completions?api-version=${this.config.apiVersion}`;
  
      const response = await this.aiClient.post(
        url,
        {
          messages,
          model,
          max_tokens: 800,
        },
        {
          headers: {
            "api-key": this.config.apiKey,
            "Content-Type": "application/json",
          },
        }
      );
  
      if (!response.data.choices || response.data.choices.length === 0) {
        throw new Error("No response from AI service");
      }
  
      return response.data.choices[0].message.content;
    } catch (error) {
      console.error("Error calling Azure OpenAI Vision API:", error);
      throw error
    }
  }
}

