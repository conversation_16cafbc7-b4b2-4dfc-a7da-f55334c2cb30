// TypeChatService.ts
import { createJsonTranslator, createAzureOpenAILanguageModel } from "typechat";
import { createTypeScriptJsonValidator } from "typechat/ts";
import {
  IntentResponse,
  CurationCaseContextResponse,
  ReviewCaseContextResponse,
  EventOptionsResponse,
  OutfitResponse,
} from "../types/schema";

export class TypeChatService {
  private model;
  private intentValidator;
  private reviewContextValidator;
  private curationContextValidator;
  private optionsValidator;
  private outfitValidator;
  private schema;

  constructor() {
    console.log("[TYPECHAT] Initializing TypeChat service");
    const endpoint = `https://whatsapp-ai-stylist-monova.openai.azure.com/openai/deployments/gpt-4o-mini/chat/completions?api-version=2024-06-01`;

    this.model = createAzureOpenAILanguageModel(
      process.env.AZURE_OPENAI_API_KEY || "********************************",
      endpoint
    );
    this.schema = `
    declare type String = string;
    declare type Boolean = boolean;
    declare type Number = number;
    declare type Null = null;
    
    type JsonObject = { [key: string]: string };

    export interface IntentResponse {
      classifiedIntent: "OUTFIT_CURATION" | "REVIEW_OUTFIT" | "AMA" | null;
      followUpMessage: string | null;
    }

    export interface CaseContextResponse {
      eventType: string | null;
      eventTime:
        | "EARLY MORNING"
        | "MORNING"
        | "AFTERNOON"
        | "EVENING"
        | "NIGHT"
        | null;
    }

    export interface CurationCaseContextResponse extends CaseContextResponse {
      eventOutfitVibe:
        | "BOLD"
        | "PLAYFUL"
        | "CASUAL"
        | "EDGY"
        | "MINIMAL"
        | "ETHNIC"
        | "BOHEMIAN"
        | "SPORTY"
        | "ELEGANT"
        | "PROFESSIONAL"
        | "SURPRISE_ME"
        | null;
    }

    export interface ReviewCaseContextResponse extends CaseContextResponse {}

    export interface EventOptionsResponse {
      options: string[];
    }

    export interface OutfitResponse {
      TopWear: string;
      BottomWear: string;
      Accessories: string;
      Shoes: string;
    }`;

    this.intentValidator = createTypeScriptJsonValidator<IntentResponse>(
      this.schema,
      "IntentResponse"
    );

    this.curationContextValidator =
      createTypeScriptJsonValidator<CurationCaseContextResponse>(
        this.schema,
        "CurationCaseContextResponse"
      );

    this.reviewContextValidator =
      createTypeScriptJsonValidator<ReviewCaseContextResponse>(
        this.schema,
        "ReviewCaseContextResponse"
      );

    this.optionsValidator = createTypeScriptJsonValidator<EventOptionsResponse>(
      this.schema,
      "EventOptionsResponse"
    );
    this.outfitValidator = createTypeScriptJsonValidator<OutfitResponse>(
      this.schema,
      "OutfitResponse"
    );

  }

  async parseIntentResponse(responseStr: string): Promise<IntentResponse> {
    console.log("[TYPECHAT] Parsing intent response");
    return this.parseWithValidator(responseStr, this.intentValidator);
  }

  async parseCurationContextResponse(
    responseStr: string
  ): Promise<CurationCaseContextResponse> {
    console.log("[TYPECHAT] Parsing curation case context response");
    return this.parseWithValidator(responseStr, this.curationContextValidator);
  }

  async parseReviewContextResponse(
    responseStr: string
  ): Promise<ReviewCaseContextResponse> {
    console.log("[TYPECHAT] Parsing review case context response");
    return this.parseWithValidator(responseStr, this.reviewContextValidator);
  }

  async parseOptionsResponse(responseStr: string): Promise<string[]> {
    console.log("[TYPECHAT] Parsing options response");
    try {
      // First try parsing directly as JSON array
      try {
        const directParsed = JSON.parse(responseStr);
        if (
          Array.isArray(directParsed) &&
          directParsed.every((item) => typeof item === "string")
        ) {
          console.log("[TYPECHAT] Successfully parsed direct array response");
          return directParsed;
        }
      } catch (e) {
        console.log(
          "[TYPECHAT] Direct array parsing failed, trying wrapped object"
        );
      }

      // If direct parsing fails, try parsing with validator
      const res: EventOptionsResponse = await this.parseWithValidator(
        JSON.stringify({ options: responseStr }),
        this.optionsValidator
      );
      return res.options;
    } catch (error) {
      console.error("[TYPECHAT] Error parsing options response:", error);
      throw new Error("Failed to parse options response: " + error);
    }
  }

  async parseOutfitResponse(responseStr: string): Promise<OutfitResponse> {
    console.log("[TYPECHAT] Parsing outfit response");
    return this.parseWithValidator(responseStr, this.outfitValidator);
  }

  private async parseWithValidator<T>(
    responseStr: string,
    validator: ReturnType<typeof createTypeScriptJsonValidator>
  ) {
    try {
      console.log("[TYPECHAT] Parsing response");
      const translator = createJsonTranslator(this.model, validator);
      console.log("[TYPECHAT] Translating response");
      const response = await translator.translate(responseStr);

      if (!response.success) {
        console.error("[TYPECHAT] Parsing failed:", response.message);
        throw new Error(`TypeChat parsing failed: ${response.message}`);
      }

      console.log(
        "[TYPECHAT] Successfully parsed response",
        JSON.stringify(response.data)
      );
      return response.data as T;
    } catch (error) {
      console.error("[TYPECHAT] Error during parsing:", error);
      throw error;
    }
  }
}
