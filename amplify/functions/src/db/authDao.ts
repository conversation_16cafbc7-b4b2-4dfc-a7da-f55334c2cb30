import { generateClient } from "aws-amplify/api";
import { type Schema } from "../../../data/resource";
import { Amplify } from "aws-amplify";
import outputs from "../../../../amplify_outputs.json"
Amplify.configure(outputs);

export class AuthDao {
  private client;

  constructor() {
    console.log("[AUTH][DAO] Initializing AuthDao...");
    try {
      this.client = generateClient<Schema>();
      console.log("[AUTH][DAO] Client generated successfully:", {
        clientExists: !!this.client,
        modelsExist: !!this.client?.models,
        otpRecordExists: !!this.client?.models?.otpRecord
      });
    } catch (error) {
      console.error("[AUTH][DAO] Error initializing AuthDao:", error);
      throw error;
    }
  }

  async createOTPRecord(userId: string, otp: string, expiresAt: number) {
    console.log("[AUTH][DAO] Creating OTP record:", { 
      userId, 
      expiresAt 
    });

    try {
      const result = await this.client.models.otpRecord.create({
        userId,
        otp,
        expiresAt
      });

      console.log("[AUTH][DAO] OTP record created successfully:", {
        userId,
        expiresAt
      });

      return result;
    } catch (error) {
      console.error("[AUTH][DAO] Error creating OTP record:", {
        error,
        userId,
        errorType: error instanceof Error ? error.constructor.name : 'Unknown',
        errorMessage: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  async getOTPRecord(userId: string) {
    console.log("[AUTH][DAO] Getting OTP record for userId:", userId);

    try {
      const result = await this.client.models.otpRecord.get({
        userId
      });

      console.log("[AUTH][DAO] OTP record retrieved:", {
        userId,
        found: !!result?.data
      });

      return result;
    } catch (error) {
      console.error("[AUTH][DAO] Error getting OTP record:", {
        error,
        userId,
        errorType: error instanceof Error ? error.constructor.name : 'Unknown',
        errorMessage: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  async deleteOTPRecord(userId: string) {
    console.log("[AUTH][DAO] Deleting OTP record for userId:", userId);

    try {
      const result = await this.client.models.otpRecord.delete({
        userId
      });

      console.log("[AUTH][DAO] OTP record deleted successfully:", {
        userId
      });

      return result;
    } catch (error) {
      console.error("[AUTH][DAO] Error deleting OTP record:", {
        error,
        userId,
        errorType: error instanceof Error ? error.constructor.name : 'Unknown',
        errorMessage: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  async updateOTPRecord(userId: string, updates: {
    otp?: string;
    expiresAt?: number;
  }) {
    console.log("[AUTH][DAO] Updating OTP record:", {
      userId,
      updates
    });

    try {
      const result = await this.client.models.otpRecord.update({
        userId,
        ...updates
      });

      console.log("[AUTH][DAO] OTP record updated successfully:", {
        userId
      });

      return result;
    } catch (error) {
      console.error("[AUTH][DAO] Error updating OTP record:", {
        error,
        userId,
        errorType: error instanceof Error ? error.constructor.name : 'Unknown',
        errorMessage: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }
}