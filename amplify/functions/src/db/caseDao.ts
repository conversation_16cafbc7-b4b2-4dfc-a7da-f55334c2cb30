import { DynamoDBClient } from "@aws-sdk/client-dynamodb";
import {
  DynamoDBDocumentClient,
  PutCommand,
  GetCommand,
  QueryCommand,
  UpdateCommand,
} from "@aws-sdk/lib-dynamodb";
import type { ReturnValue } from "@aws-sdk/client-dynamodb";

const client = new DynamoDBClient({
  region: "us-east-1",
});
const docClient = DynamoDBDocumentClient.from(client);

const TABLE_NAME = "cases_monova_test";

export class CaseDao {
  async createCase(
    userId: string,
    caseId: string,
    caseStartTime: string,
    caseStatus:
      | "NEW"
      | "USECASE_IDENTIFIED"
      | "CONTEXT_GATHERED"
      | "OUTPUT_SENT"
      | "FEEDBACK_LOOP"
      | "CLOSED" = "NEW",
    messages: string[] = []
  ) {
    console.log("Creating case with params:", {
      userId,
      caseId,
      caseStartTime,
      caseStatus,
      messages,
    });

    try {
      const params = {
        TableName: TABLE_NAME,
        Item: {
          userId,
          caseId,
          caseStartTime,
          caseStatus,
          messages,
          createdAt: Date.now(),
          updatedAt: Date.now(),
        },
      };
      await docClient.send(new PutCommand(params));
      console.log("Case created successfully:", params.Item);
      return params.Item;
    } catch (error) {
      console.error("Error creating case:", {
        error,
        params: { userId, caseId, caseStatus },
      });
      throw error;
    }
  }

  async updateCase(
    userId: string,
    caseId: string,
    updates: {
      caseEndTime?: string;
      caseStatus?:
        | "NEW"
        | "USECASE_IDENTIFIED"
        | "CONTEXT_GATHERED"
        | "OUTPUT_SENT"
        | "FEEDBACK_LOOP"
        | "CLOSED";
      messages?: string[];
      caseContext?: any;
      caseTitle?: string;
    }
  ) {
    console.log("Updating case:", { userId, caseId, updates });

    try {
      console.log("Adding updatedAt timestamp to updates");
      Object.defineProperty(updates, "updatedAt", {
        value: Date.now(),
        enumerable: true,
      });
      console.log("Updates with updatedAt:", updates);
      let updateExpression = "set";
      const expressionAttributeValues: any = {};
      const expressionAttributeNames: any = {};

      Object.entries(updates).forEach(([key, value], index) => {
        const prefix = index === 0 ? " " : ", ";
        updateExpression += `${prefix}#${key} = :${key}`;
        expressionAttributeValues[`:${key}`] = value;
        expressionAttributeNames[`#${key}`] = key;
      });

      const params = {
        TableName: TABLE_NAME,
        Key: {
          userId,
          caseId,
        },
        UpdateExpression: updateExpression,
        ExpressionAttributeValues: expressionAttributeValues,
        ExpressionAttributeNames: expressionAttributeNames,
        ReturnValues: "UPDATED_NEW" as ReturnValue,
      };

      const result = await docClient.send(new UpdateCommand(params));
      console.log("Case updated successfully:", result.Attributes);
      return result.Attributes;
    } catch (error) {
      console.error("Error updating case:", {
        error,
        params: { userId, caseId, updates },
      });
      throw error;
    }
  }

  async getCaseById(userId: string, caseId: string) {
    console.log("Getting case by ID:", { userId, caseId });
    try {
      const params = {
        TableName: TABLE_NAME,
        Key: {
          userId,
          caseId,
        },
      };
      const { Item } = await docClient.send(new GetCommand(params));
      console.log("Case retrieval result:", Item);
      return Item;
    } catch (error) {
      console.error("Error getting case:", {
        error,
        params: { userId, caseId },
      });
      throw error;
    }
  }

  async listCasesByUser(userId: string, limit: number = 50, startKey?: any) {
    console.log("Listing cases for user:", { userId, limit, startKey });
    try {
      const params: any = {
        TableName: TABLE_NAME,
        KeyConditionExpression: "userId = :userId",
        ExpressionAttributeValues: {
          ":userId": userId,
        },
        Limit: limit,
      };

      if (startKey) {
        params.ExclusiveStartKey = startKey;
      }

      const { Items, LastEvaluatedKey } = await docClient.send(
        new QueryCommand(params)
      );
      console.log("Cases listed successfully:", {
        count: Items?.length,
        hasMore: !!LastEvaluatedKey,
      });

      return {
        items: Items,
        lastEvaluatedKey: LastEvaluatedKey,
      };
    } catch (error) {
      console.error("Error listing cases:", {
        error,
        userId,
      });
      throw error;
    }
  }
}
