import { DynamoDBClient } from "@aws-sdk/client-dynamodb";
import {
  DynamoDBDocumentClient,
  PutCommand,
  GetCommand,
  QueryCommand,
  UpdateCommand,
} from "@aws-sdk/lib-dynamodb";
import { ReturnValue } from "@aws-sdk/client-dynamodb";
import {
  MessageType,
  MessagePlatform,
  MessageOrigin,
} from "../config/constants";

const client = new DynamoDBClient({
  region: "us-east-1",
});
const docClient = DynamoDBDocumentClient.from(client);

const TABLE_NAME = "messages_monova_test";

/**
 *  Messages: a
     .model({
       caseId: a.string().required(),
       messageId: a.string().required(),
       userId: a.string().required(),
       messageDirection: a.enum(["INBOUND", "OUTBOUND"]),
       messageStatus: a.enum(["SENT", "DELIVERED", "READ", "RECEIVED"]),
       messageType: a.enum(["TEXT", "IMAGE", "INTERACTIVE"]),
       messageContent: a.string().required(),
     })
     .identifier(["caseId", "messageId"])
     .authorization((allow) => [allow.guest()]),

 */

export class MessageDao {
  async createMessage(
    caseId: string,
    messageId: string,
    userId: string,
    messageDirection: "INBOUND" | "OUTBOUND",
    messageStatus: "SENT" | "DELIVERED" | "READ" | "RECEIVED",
    messageType: "TEXT" | "IMAGE" | "INTERACTIVE" | "LOG",
    messageContent: any,
    messagePlatform: MessagePlatform,
    messageOrigin: MessageOrigin,
    messageContext?: any
  ) {
    console.log("=== Creating Message Record ===", {
      caseId,
      messageId,
      userId,
      messageDirection,
      messageStatus,
      messageType,
      messageContent,
      messagePlatform,
      messageOrigin,
    });

    try {
      let contentToStore = messageContent;

      // Step 1: Handle message content based on type
      if (messageType === "TEXT") {
        messageContent = messageContent?.body?.body; // Store only the body for text messages
      }

      if (messageContext) {
        // Step 2: If there is a messageContext, retrieve the original message
        const originalMessage = await this.getMessageById(
          caseId,
          messageContext.id
        );
        if (originalMessage?.data) {
          const originalContent = JSON.parse(
            originalMessage?.data?.messageContent
          );
          console.log(
            `[messageDao][createMessage] tid=${messageId} originalMessage -> ${JSON.stringify(
              originalMessage,
              null,
              2
            )}`
          );
          console.log(
            `[messageDao][createMessage] tid=${messageId} parsed originalContent -> ${JSON.stringify(
              originalContent,
              null,
              2
            )}`
          );
          const originalMessageType = originalMessage?.data?.messageType;

          if (
            originalMessageType?.toLowerCase() ===
            MessageType.TEXT.toLowerCase()
          ) {
            // If the original message was text, set replyToMessage
            contentToStore = {
              replyToMessage: originalContent,
              currentMessage: messageContent, // Assuming messageContent.body is the current message
            };
          } else if (
            originalMessageType?.toLowerCase() ===
            MessageType.INTERACTIVE.toLowerCase()
          ) {
            // If the original message was interactive, set replyToMessage and currentMessage
            let currentMessage;
            if (messageContent?.type === "button_reply") {
              currentMessage = messageContent?.button_reply?.id;
            } else {
              currentMessage = messageContent?.list_reply?.id;
            }
            contentToStore = {
              replyToMessage: originalContent?.body?.text, // Assuming body.text exists in interactive content
              currentMessage: currentMessage,
            };
          } else if (
            originalMessageType?.toLowerCase() ===
            MessageType.IMAGE.toLowerCase()
          ) {
            contentToStore = {
              replyToMessage: {
                url: originalContent?.url,
                caption: originalContent?.caption,
              },
              currentMessage: messageContent,
            };
          } else {
            contentToStore = {
              replyToMessage: originalContent,
              caption: messageContent,
            };
          }
        }
      }

      console.log(
        `Message content to store: ${JSON.stringify(contentToStore, null, 2)}`
      );

      if (
        messageType.toLowerCase() ===
          MessageType.INTERACTIVE_LIST.toLowerCase() ||
        messageType.toLowerCase() ===
          MessageType.INTERACTIVE_REPLY.toLowerCase()
      ) {
        messageType = "INTERACTIVE";
      }

      const createdAt = Date.now();
      console.log("Attempting to create message record in database");

      const params = {
        TableName: TABLE_NAME,
        Item: {
          caseId,
          messageId,
          userId,
          createdAt,
          messageDirection,
          messageStatus,
          messageType,
          messageContent: JSON.stringify(contentToStore),
          messagePlatform,
          messageOrigin,
        },
      };
      await docClient.send(new PutCommand(params));
      return messageId;
    } catch (error) {
      console.error(
        `[messageDao][createMessage] tid=${messageId} error -> ${error}`
      );
      throw error;
    }
  }

  async getMessageById(caseId: string, messageId: string) {
    try {
      console.log("[MESSAGE_DAO_2][getMessageById] Getting message", {
        caseId,
        messageId,
      });
      const params = {
        TableName: TABLE_NAME,
        Key: {
          caseId,
          messageId,
        },
      };

      const { Item } = await docClient.send(new GetCommand(params));
      console.log("[MESSAGE_DAO_2][getMessageById] Retrieved message:", Item);
      return Item;
    } catch (error) {
      console.error("[MESSAGE_DAO_2][getMessageById] Error:", error);
      throw error;
    }
  }

  async listMessagesByCase(caseId: string, messageId?: string) {
    try {
      console.log(
        "[MESSAGE_DAO_2][listMessagesByCase] Listing messages for case",
        { caseId, messageId }
      );
      const params = {
        TableName: TABLE_NAME,
        KeyConditionExpression: "caseId = :caseId",
        ExpressionAttributeValues: {
          ":caseId": caseId,
        },
      };
      const { Items } = await docClient.send(new QueryCommand(params));
      console.log(
        "[MESSAGE_DAO_2][listMessagesByCase] Retrieved messages count:",
        Items?.length,
        Items
      );
      Items?.forEach((item) => {
        if (item.messageContent) {
          item.messageContent = Buffer.from(
            item.messageContent,
            "utf-8"
          ).toString("utf-8");
        }
      });
      return Items;
    } catch (error) {
      console.error("[MESSAGE_DAO_2][listMessagesByCase] Error:", error);
      throw error;
    }
  }

  async updateMessage(
    caseId: string,
    messageId: string,
    updates: {
      messageStatus?: "SENT" | "DELIVERED" | "READ" | "RECEIVED";
      messageContent?: any;
    }
  ) {
    try {
      console.log("[MESSAGE_DAO_2][updateMessage] Updating message", {
        caseId,
        messageId,
        updates,
      });

      let updateExpression = "set";
      const expressionAttributeValues: any = {};
      const expressionAttributeNames: any = {};

      if (updates.messageStatus) {
        updateExpression += " #status = :status";
        expressionAttributeValues[":status"] = updates.messageStatus;
        expressionAttributeNames["#status"] = "messageStatus";
      }

      if (updates.messageContent) {
        updateExpression += updates.messageStatus ? "," : "";
        updateExpression += " #content = :content";
        expressionAttributeValues[":content"] = JSON.stringify(
          updates.messageContent
        );
        expressionAttributeNames["#content"] = "messageContent";
      }

      const params = {
        TableName: TABLE_NAME,
        Key: {
          caseId,
          messageId,
        },
        UpdateExpression: updateExpression,
        ExpressionAttributeValues: expressionAttributeValues,
        ExpressionAttributeNames: expressionAttributeNames,
        ReturnValues: "UPDATED_NEW" as ReturnValue,
      };

      const result = await docClient.send(new UpdateCommand(params));
      console.log("[MESSAGE_DAO_2][updateMessage] Updated message:", result);
      return result.Attributes;
    } catch (error) {
      console.error("[MESSAGE_DAO_2][updateMessage] Error:", error);
      throw error;
    }
  }

  async updateMessageStatuses(
    updates: Array<{
      caseId: string;
      messageId: string;
      messageStatus: "SENT" | "DELIVERED" | "READ" | "RECEIVED";
    }>
  ) {
    try {
      console.log(
        "[MESSAGE_DAO_2][updateMessageStatuses] Updating messages",
        updates
      );

      const updatePromises = updates.map(
        ({ caseId, messageId, messageStatus }) => {
          const params = {
            TableName: TABLE_NAME,
            Key: {
              caseId,
              messageId,
            },
            UpdateExpression: "set #status = :status",
            ExpressionAttributeValues: {
              ":status": messageStatus,
            },
            ExpressionAttributeNames: {
              "#status": "messageStatus",
            },
            ReturnValues: "UPDATED_NEW" as ReturnValue,
          };

          return docClient.send(new UpdateCommand(params));
        }
      );

      const results = await Promise.allSettled(updatePromises);

      // Log failures
      results.forEach((result, index) => {
        if (result.status === "rejected") {
          console.error(
            `[MESSAGE_DAO_2][updateMessageStatuses] Failed to update message:`,
            {
              caseId: updates[index].caseId,
              messageId: updates[index].messageId,
              error: result.reason,
            }
          );
        }
      });
      console.log(
        `[MESSAGE_DAO_2][updateMessageStatuses] Updated messages:`,
        results
      );
      return results;
    } catch (error) {
      console.error("[MESSAGE_DAO_2][updateMessageStatuses] Error:", error);
      throw error;
    }
  }
}
