import { DynamoDBClient } from "@aws-sdk/client-dynamodb";
import {
  DynamoDBDocumentClient,
  PutCommand,
  GetCommand,
  QueryCommand,
  UpdateCommand,
  DeleteCommand,
} from "@aws-sdk/lib-dynamodb";
import { v4 as uuidv4 } from "uuid";
import {
  Outfit,
  CreateOutfitInput,
  UpdateOutfitInput,
  ListOutfitsInput,
  ListOutfitsOutput,
  UpdateOutfitOutput,
} from "../functions/outfitController/types";

const client = new DynamoDBClient({ region: "us-east-1" });
const docClient = DynamoDBDocumentClient.from(client);

const TABLE_NAME = "outfits_monova_dev";

export class OutfitDao {
  async createOutfit(input: CreateOutfitInput): Promise<Outfit> {
    console.log("[OutfitDao][createOutfit] Creating outfit with input:", input);

    try {
      const timestamp = Date.now();
      const outfit: Outfit = {
        ...input,
        outfitId: `${Number.MAX_SAFE_INTEGER - Date.now()}-${uuidv4()}`,
        createdAt: timestamp,
        updatedAt: timestamp,
      };

      await docClient.send(
        new PutCommand({
          TableName: TABLE_NAME,
          Item: outfit,
        })
      );

      console.log(
        "[OutfitDao][createOutfit] Outfit created successfully:",
        outfit
      );
      return outfit;
    } catch (error) {
      console.error("[OutfitDao][createOutfit] Error:", error);
      throw error;
    }
  }

  async getOutfitById(
    userId: string,
    outfitId: string
  ): Promise<Outfit | null> {
    console.log("[OutfitDao][getOutfitById] Getting outfit:", {
      userId,
      outfitId,
    });

    try {
      const { Item } = await docClient.send(
        new GetCommand({
          TableName: TABLE_NAME,
          Key: { userId, outfitId },
        })
      );

      console.log("[OutfitDao][getOutfitById] Retrieved outfit:", Item);
      return (Item as Outfit) || null;
    } catch (error) {
      console.error("[OutfitDao][getOutfitById] Error:", error);
      throw error;
    }
  }

  async listOutfits(input: ListOutfitsInput): Promise<ListOutfitsOutput> {
    console.log("[OutfitDao][listOutfits] Listing outfits:", input);

    try {
      const params: any = {
        TableName: TABLE_NAME,
        KeyConditionExpression: "userId = :userId",
        ExpressionAttributeValues: {
          ":userId": input.userId,
        },
        Limit: input.limit || 50,
      };

      if (input.startKey) {
        params.ExclusiveStartKey = input.startKey;
      }

      const { Items, LastEvaluatedKey } = await docClient.send(
        new QueryCommand(params)
      );

      console.log("[OutfitDao][listOutfits] Retrieved outfits:", {
        count: Items?.length,
        hasMore: !!LastEvaluatedKey,
      });

      return {
        outfits: Items as Outfit[],
        lastEvaluatedKey: LastEvaluatedKey,
      };
    } catch (error) {
      console.error("[OutfitDao][listOutfits] Error:", error);
      throw error;
    }
  }

  async updateOutfit(
    userId: string,
    outfitId: string,
    updates: UpdateOutfitInput
  ): Promise<UpdateOutfitOutput> {
    console.log("[OutfitDao][updateOutfit] Updating outfit:", {
      userId,
      outfitId,
      updates,
    });

    try {
      let updateExpression = "set updatedAt = :updatedAt";
      const expressionAttributeValues: any = { ":updatedAt": Date.now() };
      const expressionAttributeNames: any = {};

      Object.entries(updates).forEach(([key, value]) => {
        updateExpression += `, #${key} = :${key}`;
        expressionAttributeValues[`:${key}`] = value;
        expressionAttributeNames[`#${key}`] = key;
      });

      const result = await docClient.send(
        new UpdateCommand({
          TableName: TABLE_NAME,
          Key: { userId, outfitId },
          UpdateExpression: updateExpression,
          ExpressionAttributeValues: expressionAttributeValues,
          ExpressionAttributeNames: expressionAttributeNames,
          ReturnValues: "ALL_NEW",
        })
      );

      console.log(
        "[OutfitDao][updateOutfit] Outfit updated successfully:",
        result.Attributes
      );
      return result.Attributes as UpdateOutfitOutput;
    } catch (error) {
      console.error("[OutfitDao][updateOutfit] Error:", error);
      throw error;
    }
  }

  async deleteOutfit(userId: string, outfitId: string): Promise<void> {
    console.log("[OutfitDao][deleteOutfit] Deleting outfit:", {
      userId,
      outfitId,
    });

    try {
      await docClient.send(
        new DeleteCommand({
          TableName: TABLE_NAME,
          Key: { userId, outfitId },
        })
      );

      console.log("[OutfitDao][deleteOutfit] Outfit deleted successfully");
    } catch (error) {
      console.error("[OutfitDao][deleteOutfit] Error:", error);
      throw error;
    }
  }
}
