import { generateClient } from "aws-amplify/api";
import { type Schema } from "../../../data/resource";
import { Amplify } from "aws-amplify";
import outputs from "../../../../amplify_outputs.json";
Amplify.configure(outputs);

export class SocketDao {
  private client;

  constructor() {
    console.log("Initializing  Connection Dao");
    try {
      this.client = generateClient<Schema>();
      console.log("Client generated successfully:", !!this.client);
      console.log("Available models: ", {
        models: Object.keys(this.client || {}),
        SocketConnection: !!this.client?.models?.SocketConnection,
      });
    } catch (error) {
      console.log("Error generating client : ", error);
      throw error;
    }
  }
  async createConnection(
    userId: string,
    connectionId: string,
    createdAt: string
  ) {
    console.log("Creating Connection with params:", {
      connectionId,
      userId,
      createdAt,
    });

    try {
      if (!this.client?.models?.SocketConnection) {
        console.error("Client or SocketConnection model not available:", {
          clientExists: !!this.client,
          SocketConnectionExists: !!this.client?.models?.SocketConnection,
        });
        throw new Error("SocketConnection model not initialized");
      }
      const result = await this.client.models.SocketConnection.create({
        connectionId,
        userId,
        createdAt,
      });
      console.log("Connection created successfully:", result);
      return result;
    } catch (error) {
      console.error("Error creating connection:", error);
      throw error;
    }
  }
  async deleteConnection(userId: string) {
    console.log("Deleting Connection with UserId:", userId);
    try {
      if (!this.client?.models?.SocketConnection) {
        console.error("Client or SocketConnection model not available:", {
          clientExists: !!this.client,
          SocketConnectionExists: !!this.client?.models?.SocketConnection,
        });
        throw new Error("SocketConnection model not initialized");
      }
      const result = await this.client.models.SocketConnection.delete({
        userId,
      });
      console.log("Connection deleted successfully:", result);
      return result;
    } catch (error) {
      console.error("Error deleting connection:", error);
      throw error;
    }
  }
  async getConnection(userId: string) {
    console.log("Getting Connection with userId:", userId);
    try {
      if (!this.client?.models?.SocketConnection) {
        console.error("Client or SocketConnection model not available:", {
          clientExists: !!this.client,
          SocketConnectionExists: !!this.client?.models?.SocketConnection,
        });
        throw new Error("SocketConnection model not initialized");
      }
      const result = await this.client.models.SocketConnection.get({
        userId,
      });
      console.log("Connection retrieved successfully:", result);
      return result;
    } catch (error) {
      console.error("Error getting connection:", error);
      throw error;
    }
  }
  async updateConnection(
    userId: string,
    connectionId: string,
    createdAt: string
  ) {
    console.log("Updating Connection with userId:", userId);
    try {
      if (!this.client?.models?.SocketConnection) {
        console.error("Client or SocketConnection model not available:", {
          clientExists: !!this.client,
          SocketConnectionExists: !!this.client?.models?.SocketConnection,
        });
        throw new Error("SocketConnection model not initialized");
      }
      const result = await this.client.models.SocketConnection.update({
        userId: userId,
        connectionId: connectionId,
        createdAt: createdAt,
      });
      console.log("Connection updated successfully:", result);
      return result;
    } catch (error) {
      console.error("Error updating connection:", error);
      throw error;
    }
  }
}
