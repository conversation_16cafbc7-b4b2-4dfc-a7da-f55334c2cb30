import { generateClient } from "aws-amplify/data";
import { type Schema } from "../../../data/resource";
import { Amplify } from "aws-amplify";
import outputs from "../../../../amplify_outputs.json";
import { FemaleBodyType, MaleBodyType } from "../config/constants";
Amplify.configure(outputs);

export class UserStyleProfileDao {
  private client;

  constructor() {
    this.client = generateClient<Schema>();
  }

  /**
   * Creates a new user style profile record
   */
  async createUserStyleProfile(
    userId: string,
    userBodyType?: MaleBodyType | FemaleBodyType,
    userSkinUnderTone?: "COOL" | "WARM" | "NEUTRAL" | "TANNED" | "DARK",
    userAge?: "AGE_0_20" | "AGE_21_24" | "AGE_25_30" | "AGE_31_36" | "AGE_36_99"
  ) {
    try {
      console.log(
        "[UserStyleProfileDao][createUserStyleProfile] Creating user style profile:",
        {
          userId,
          userBodyType,
          userSkinUnderTone,
          userAge,
        }
      );
      return await this.client.models.UserStyleProfile.create({
        userId,
        userBodyType,
        userSkinUnderTone,
        userAge,
      });
    } catch (error) {
      console.error(
        "[UserStyleProfileDao][createUserStyleProfile] Error creating user style profile:",
        {
          error,
          userId,
          errorType:
            error instanceof Error ? error.constructor.name : "Unknown",
          errorMessage:
            error instanceof Error ? error.message : "Unknown error",
        }
      );
      throw error;
    }
  }

  /**
   * Updates an existing user style profile
   */
  async updateUserStyleProfile(
    userId: string,
    updates: {
      userBodyType?: MaleBodyType | FemaleBodyType;
      userSkinUnderTone?: "COOL" | "WARM" | "NEUTRAL" | "TANNED" | "DARK";
      userAge?:
        | "AGE_0_20"
        | "AGE_21_24"
        | "AGE_25_30"
        | "AGE_31_36"
        | "AGE_36_99";
      userGender?: "MALE" | "FEMALE";
    }
  ) {
    try {
      console.log(
        "[UserStyleProfileDao][updateUserStyleProfile] Updating user style profile:",
        {
          userId,
          updates,
        }
      );
      return await this.client.models.UserStyleProfile.update({
        userId,
        ...updates,
      });
    } catch (error) {
      console.error(
        "[UserStyleProfileDao][updateUserStyleProfile] Error updating user style profile:",
        {
          error,
          userId,
          updates,
          errorType:
            error instanceof Error ? error.constructor.name : "Unknown",
          errorMessage:
            error instanceof Error ? error.message : "Unknown error",
        }
      );
      throw error;
    }
  }

  /**
   * Retrieves user style profile by userId
   */
  async getUserStyleProfileById(userId: string) {
    try {
      console.log(
        "[UserStyleProfileDao][getUserStyleProfileById] Fetching user style profile:",
        {
          userId,
        }
      );
      return await this.client.models.UserStyleProfile.get({ userId });
    } catch (error) {
      console.error(
        "[UserStyleProfileDao][getUserStyleProfileById] Error getting user style profile:",
        {
          error,
          userId,
          errorType:
            error instanceof Error ? error.constructor.name : "Unknown",
          errorMessage:
            error instanceof Error ? error.message : "Unknown error",
        }
      );
      throw error;
    }
  }

  async deleteUserStyleProfile(userId: string) {
    try {
      console.log(
        "[UserStyleProfileDao][deleteUserStyleProfile] Deleting user style profile:",
        {
          userId,
        }
      );
      return await this.client.models.UserStyleProfile.delete({
        userId: userId,
      });
    } catch (error) {
      console.error(
        "[UserStyleProfileDao][deleteUserStyleProfile] Error deleting user style profile:",
        {
          error,
          userId,
          errorType:
            error instanceof Error ? error.constructor.name : "Unknown",
          errorMessage:
            error instanceof Error ? error.message : "Unknown error",
        }
      );
      throw error;
    }
  }
}
