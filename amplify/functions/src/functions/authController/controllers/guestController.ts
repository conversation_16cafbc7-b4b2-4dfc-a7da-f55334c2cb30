import { APIGatewayProxyEvent, APIGatewayProxyResult } from "aws-lambda";
import { AuthService } from "../../../services/authService";
import { 
  formatSuccessResponse, 
  formatErrorResponse, 
  HTTP_STATUS, 
  ERROR_CODES 
} from "../../../utils/responseFormatter";

export class GuestController {
  private authService: AuthService;

  constructor() {
    this.authService = new AuthService();
  }

  async createGuestUser(event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> {
    console.log(`[AUTH][GUEST] Request received:`, event.body);
    try {
      console.log(`[AUTH][GUEST] Creating guest user`);
      const result = await this.authService.createGuestUser();
      console.log(`[AUTH][GUEST] Guest user created successfully`, {
        result,
      });
      
      return formatSuccessResponse(
        result,
        "Guest user created successfully",
        HTTP_STATUS.CREATED
      );
    } catch (error) {
      console.error(`[AUTH][GUEST] Error creating guest user:`, error);
      return formatErrorResponse(
        error instanceof Error ? error : "Failed to create guest user",
        HTTP_STATUS.INTERNAL_SERVER_ERROR,
        ERROR_CODES.INTERNAL_ERROR
      );
    }
  }
} 