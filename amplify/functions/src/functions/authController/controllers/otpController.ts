import { APIGatewayProxyEvent, APIGatewayProxyResult } from "aws-lambda";
import { AuthService } from "../../../services/authService";
import { 
  formatSuccessResponse, 
  formatErrorResponse, 
  HTTP_STATUS, 
  ERROR_CODES 
} from "../../../utils/responseFormatter";

export class OTPController {
  private authService: AuthService;

  constructor() {
    this.authService = new AuthService();
  }

  async generateSignupOTP(event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> {
    console.log(`[AUTH][SIGNUP] Request received:`, event.body);
    
    const body = JSON.parse(event.body || '{}');
    if (!body.userId) {
      return formatErrorResponse(
        "userId is required",
        HTTP_STATUS.BAD_REQUEST,
        ERROR_CODES.VALIDATION_ERROR
      );
    }

    try {
      console.log(`[AUTH][SIGNUP] Generating OTP for userId: ${body.userId}`);
      const result = await this.authService.generateSignupOTP(body.userId);
      return formatSuccessResponse(result, "OTP sent successfully");
    } catch (error) {
      console.error(`[AUTH][SIGNUP] Error generating OTP:`, error);
      if (error instanceof Error) {
        switch (error.message) {
          case "User already exists":
            return formatErrorResponse(
              error,
              HTTP_STATUS.CONFLICT,
              ERROR_CODES.CONFLICT
            );
          case "Invalid phone number format":
            return formatErrorResponse(
              error,
              HTTP_STATUS.BAD_REQUEST,
              ERROR_CODES.VALIDATION_ERROR
            );
        }
      }
      throw error;
    }
  }

  async generateLoginOTP(event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> {
    console.log(`[AUTH][LOGIN] Request received:`, event.body);
    
    const body = JSON.parse(event.body || '{}');
    if (!body.userId) {
      return formatErrorResponse(
        "userId is required",
        HTTP_STATUS.BAD_REQUEST,
        ERROR_CODES.VALIDATION_ERROR
      );
    }

    try {
      console.log(`[AUTH][LOGIN] Generating OTP for userId: ${body.userId}`);
      const result = await this.authService.generateLoginOTP(body.userId);
      return formatSuccessResponse(result, "OTP sent successfully");
    } catch (error) {
      console.error(`[AUTH][LOGIN] Error generating OTP:`, error);
      if (error instanceof Error) {
        switch (error.message) {
          case "User not found":
            return formatErrorResponse(
              error,
              HTTP_STATUS.NOT_FOUND,
              ERROR_CODES.NOT_FOUND
            );
          case "Invalid phone number format":
            return formatErrorResponse(
              error,
              HTTP_STATUS.BAD_REQUEST,
              ERROR_CODES.VALIDATION_ERROR
            );
        }
      }
      throw error;
    }
  }

  async verifyOTP(event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> {
    console.log(`[AUTH][VERIFY] Request received:`, event.body);
    
    const body = JSON.parse(event.body || '{}');
    if (!body.userId || !body.otp) {
      return formatErrorResponse(
        "userId and otp are required",
        HTTP_STATUS.BAD_REQUEST,
        ERROR_CODES.VALIDATION_ERROR
      );
    }

    try {
      console.log(`[AUTH][VERIFY] Verifying OTP for userId: ${body.userId}`);
      const result = await this.authService.verifyOTP(
        body.userId,
        body.otp,
        body.userFullName
      );

      const cookieSettings = [
        `authToken=${result.authToken}`,
        `Domain=chat.monova.in`,
        "HttpOnly",
        "Secure",
        "SameSite=Lax",
        "Path=/",
        `Max-Age=${30 * 24 * 60 * 60}`, // 30 days
      ];

      return formatSuccessResponse(
        result,
        "OTP verified successfully",
        HTTP_STATUS.OK,
        cookieSettings
      );
    } catch (error) {
      console.error(`[AUTH][VERIFY] Error verifying OTP:`, error);
      if (error instanceof Error) {
        switch (error.message) {
          case "No OTP found":
          case "Invalid OTP":
            return formatErrorResponse(
              error,
              HTTP_STATUS.BAD_REQUEST,
              ERROR_CODES.VALIDATION_ERROR
            );
          case "OTP expired":
            return formatErrorResponse(
              error,
              HTTP_STATUS.UNAUTHORIZED,
              ERROR_CODES.AUTHENTICATION_ERROR
            );
          case "Failed to generate auth token":
            return formatErrorResponse(
              error,
              HTTP_STATUS.INTERNAL_SERVER_ERROR,
              ERROR_CODES.INTERNAL_ERROR
            );
        }
      }
      throw error;
    }
  }
} 