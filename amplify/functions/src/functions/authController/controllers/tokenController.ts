import { APIGatewayProxyEvent, APIGatewayProxyResult } from "aws-lambda";
import { AuthService } from "../../../services/authService";
import { 
  formatSuccessResponse, 
  formatErrorResponse, 
  HTTP_STATUS, 
  ERROR_CODES 
} from "../../../utils/responseFormatter";

export class TokenController {
  private authService: AuthService;

  constructor() {
    this.authService = new AuthService();
  }

  async verifyToken(event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> {
    try {
      const authHeader = event.headers.Authorization || event.headers.authorization;

      if (!authHeader?.startsWith("Bearer ")) {
        return formatErrorResponse(
          "Authorization header missing or invalid",
          HTTP_STATUS.UNAUTHORIZED,
          ERROR_CODES.AUTHENTICATION_ERROR
        );
      }

      const token = authHeader.split(" ")[1];

      try {
        const result = await this.authService.verifyAuthToken(token);
        return formatSuccessResponse(
          result,
          "Token verified successfully"
        );
      } catch (error) {
        if (error instanceof Error) {
          switch (error.message) {
            case "Token expired":
              return formatErrorResponse(
                error,
                HTTP_STATUS.UNAUTHORIZED,
                ERROR_CODES.AUTHENTICATION_ERROR
              );

            case "User not found":
              return formatErrorResponse(
                error,
                HTTP_STATUS.NOT_FOUND,
                ERROR_CODES.NOT_FOUND
              );

            case "Invalid token":
              return formatErrorResponse(
                error,
                HTTP_STATUS.UNAUTHORIZED,
                ERROR_CODES.AUTHENTICATION_ERROR
              );

            default:
              throw error;
          }
        }
        throw error;
      }
    } catch (error) {
      console.error("Error processing request:", error);
      return formatErrorResponse(
        "Internal server error",
        HTTP_STATUS.INTERNAL_SERVER_ERROR,
        ERROR_CODES.INTERNAL_ERROR
      );
    }
  }
} 