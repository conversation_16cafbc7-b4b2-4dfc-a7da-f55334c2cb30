import { APIGatewayProxy<PERSON><PERSON><PERSON>, APIGatewayProxyResult } from "aws-lambda";
import { Amplify } from "aws-amplify";
import outputs from "../../../../../amplify_outputs.json";
import { router } from "./routes";

Amplify.configure(outputs);

console.log(`[AUTH] Initialized Controller`);

export const handler: APIGatewayProxyHandler = async (
  event
): Promise<APIGatewayProxyResult> => {
  console.log("Request received:", {
    path: event.path,
    method: event.httpMethod,
    body: event.body,
  });

  try {
    return await router(event);
  } catch (error) {
    console.error("Error in handler:", error);
    return {
      statusCode: 500,
      body: JSON.stringify({ message: "Internal server error" }),
    };
  }
};
