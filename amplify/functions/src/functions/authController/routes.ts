import { APIGatewayProxyEvent, APIGatewayProxyResult } from "aws-lambda";
import { OTPController } from "./controllers/otpController";
import { TokenController } from "./controllers/tokenController";
import { GuestController } from "./controllers/guestController";

const otpController = new OTPController();
const tokenController = new TokenController();
const guestController = new GuestController();

export const router = async (
  event: APIGatewayProxyEvent
): Promise<APIGatewayProxyResult> => {
  const path = event.path.toLowerCase();

  switch (path) {
    case "/auth/otp/signup":
      return await otpController.generateSignupOTP(event);

    case "/auth/otp/login":
      return await otpController.generateLoginOTP(event);

    case "/auth/otp/verify":
      return await otpController.verifyOTP(event);

    case "/auth/token/verify":
      return await tokenController.verifyToken(event);

    case "/auth/guest/login":
      return await guestController.createGuestUser(event);

    default:
      return {
        statusCode: 404,
        body: JSON.stringify({ message: "Route not found" })
      };
  }
}; 