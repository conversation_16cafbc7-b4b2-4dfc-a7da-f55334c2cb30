import { APIGatewayProxyEvent, APIGatewayProxyResult, Context } from "aws-lambda";
import { CaseService } from "../../../services/caseService";
import type { CreateCaseInput, UpdateCaseInput, CaseStatus } from "../../../types";

export class CaseController {
    private caseService: CaseService;

    constructor() {
        this.caseService = new CaseService();
    }

    async createCase(event: APIGatewayProxyEvent, context: Context): Promise<APIGatewayProxyResult> {
        const input: CreateCaseInput = JSON.parse(event.body || "{}");
        if (!input.userId) {
            return { statusCode: 400, body: JSON.stringify({ message: "userId is required" }) };
        }
        const result = await this.caseService.createCase(input);
        return { statusCode: 201, body: JSON.stringify(result) };
    }

    async closeCase(event: APIGatewayProxyEvent, context: Context): Promise<APIGatewayProxyResult> {
        const { userId, caseId } = JSON.parse(event.body || "{}");
        if (!caseId || !userId) {
            return { statusCode: 400, body: JSON.stringify({ message: "caseId and userId are required" }) };
        }
        const result = await this.caseService.closeCase(userId, caseId);
        return { statusCode: 200, body: JSON.stringify(result) };
    }

    async closeActiveCase(event: APIGatewayProxyEvent, context: Context): Promise<APIGatewayProxyResult> {
        const { userId } = JSON.parse(event.body || "{}");
        if (!userId) {
            return { statusCode: 400, body: JSON.stringify({ message: "userId is required" }) };
        }
        const existingCase = await this.caseService.getActiveCase(userId);
        if (!existingCase) {
            return { statusCode: 404, body: JSON.stringify({ message: "No active case found" }) };
        }
        const result = await this.caseService.closeCase(userId, existingCase.caseId);
        return { statusCode: 200, body: JSON.stringify(result) };
    }

    async addMessageToCase(event: APIGatewayProxyEvent, context: Context): Promise<APIGatewayProxyResult> {
        const { userId, messageId, caseId } = JSON.parse(event.body || "{}");
        if (!caseId || !userId || !messageId) {
            return { statusCode: 400, body: JSON.stringify({ message: "caseId, userId, and messageId are required" }) };
        }
        const result = await this.caseService.addMessageToCase(userId, caseId, messageId);
        return { statusCode: 200, body: JSON.stringify(result) };
    }

    async getMessagesByCaseId(event: APIGatewayProxyEvent, context: Context): Promise<APIGatewayProxyResult> {
        const caseId = event.queryStringParameters?.caseId;
        if (!caseId) {
            return { statusCode: 400, body: JSON.stringify({ message: "caseId is required as a query parameter" }) };
        }
        try {
            const result = await this.caseService.getMessagesByCaseId(caseId);
            return { statusCode: 200, body: JSON.stringify(result) };
        } catch (error) {
            return { statusCode: 500, body: JSON.stringify({ message: "Error retrieving messages", error: error instanceof Error ? error.message : "Unknown error" }) };
        }
    }

    async updateCase(event: APIGatewayProxyEvent, context: Context): Promise<APIGatewayProxyResult> {
        const caseId = this.getCaseIdFromPath(event.path);
        const parsedBody = JSON.parse(event.body || "{}");
        const updates: UpdateCaseInput = parsedBody?.updates;
        const { userId } = JSON.parse(event.body || "{}");
        if (!userId) {
            return { statusCode: 400, body: JSON.stringify({ message: "userId is required" }) };
        }
        if (!caseId) {
            return { statusCode: 400, body: JSON.stringify({ message: "caseId is required" }) };
        }
        const result = await this.caseService.updateCase(userId, caseId, updates);
        return { statusCode: 200, body: JSON.stringify(result) };
    }

    async getCaseById(event: APIGatewayProxyEvent, context: Context): Promise<APIGatewayProxyResult> {
        const userId = event.queryStringParameters?.userId;
        if (!userId) {
            return { statusCode: 400, body: JSON.stringify({ message: "userId is required" }) };
        }
        const result = await this.caseService.getActiveCase(userId);
        return { statusCode: result ? 200 : 404, body: JSON.stringify(result || { message: "Case not found" }) };
    }

    async getActiveCase(event: APIGatewayProxyEvent, context: Context): Promise<APIGatewayProxyResult> {
        const userId = event.queryStringParameters?.userId;
        if (!userId) {
            return { statusCode: 400, body: JSON.stringify({ message: "userId is required" }) };
        }
        const result = await this.caseService.getActiveCase(userId);
        return { statusCode: 200, body: JSON.stringify(result) };
    }

    async listCases(event: APIGatewayProxyEvent, context: Context): Promise<APIGatewayProxyResult> {
        const { userId, status, limit, startKey } = event.queryStringParameters || {};
        if (!userId) {
            return { statusCode: 400, body: JSON.stringify({ message: "userId is required" }) };
        }
        const parsedLimit = limit ? parseInt(limit) : undefined;
        const parsedStartKey = startKey ? JSON.parse(decodeURIComponent(startKey)) : undefined;
        const result = await this.caseService.listCases({
            userId,
            status: status as CaseStatus,
            limit: parsedLimit,
            startKey: parsedStartKey,
        });
        return { statusCode: 200, body: JSON.stringify(result) };
    }

    private getCaseIdFromPath(path: string): string | null {
        const matches = path.match(/\/api\/cases\/([^\/]+)$/);
        return matches ? matches[1] : null;
    }
} 