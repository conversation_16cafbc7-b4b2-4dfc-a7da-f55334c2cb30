import { APIGatewayProxyHand<PERSON>, APIGatewayProxyResult } from "aws-lambda";
import { router } from "./routes";

console.log("Configuring Amplify");

export const handler: APIGatewayProxyHandler = async (
  event,
  context
): Promise<APIGatewayProxyResult> => {
  console.log("Request received:", {
    path: event.path,
    method: event.httpMethod,
    body: event.body,
    requestId: context.awsRequestId,
    timestamp: new Date().toISOString(),
  });

  try {
    return await router(event, context);
  } catch (error) {
    console.error("Error in handler:", error);
    return {
      statusCode: 500,
      body: JSON.stringify({ message: "Internal server error" }),
    };
  }
};
