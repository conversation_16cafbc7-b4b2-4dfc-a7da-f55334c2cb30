import { APIGatewayProxyEvent, APIGatewayProxyResult, Context } from "aws-lambda";
import { CaseController } from "./controllers/caseController";

const caseController = new CaseController();

export const router = async (
  event: APIGatewayProxyEvent,
  context: Context
): Promise<APIGatewayProxyResult> => {
  const path = event.path.toLowerCase();
  const method = event.httpMethod.toUpperCase();

  switch (true) {
    case method === "POST" && path === "/case/api/cases":
      return await caseController.createCase(event, context);
    case method === "PUT" && path.endsWith("/close"):
      return await caseController.closeCase(event, context);
    case method === "PUT" && path.endsWith("/active/close"):
      return await caseController.closeActiveCase(event, context);
    case method === "PUT" && path.endsWith("/messages"):
      return await caseController.addMessageToCase(event, context);
    case method === "GET" && path.endsWith("/messages"):
      return await caseController.getMessagesByCaseId(event, context);
    case method === "PUT" && !path.endsWith("/close") && !path.endsWith("/messages"):
      return await caseController.updateCase(event, context);
    case method === "GET" && path.includes("/api/cases/") && !path.includes("/active/"):
      return await caseController.getCaseById(event, context);
    case method === "GET" && path.includes("/active/"):
      return await caseController.getActiveCase(event, context);
    case method === "GET" && path === "/case/api/cases":
      return await caseController.listCases(event, context);
    default:
      return {
        statusCode: 404,
        body: JSON.stringify({ message: "Route not found" })
      };
  }
}; 