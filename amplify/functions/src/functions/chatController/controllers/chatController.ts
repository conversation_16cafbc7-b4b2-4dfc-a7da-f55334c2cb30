import { APIGatewayProxyEvent, APIGatewayProxyResult, Context } from "aws-lambda";
import { MessagePlatform, MessageDirection, MessageOrigin } from "../../../config/constants";
import { ChatService } from "../../../services/chatService";
import { CaseService } from "../../../services/caseService";
import { AIService } from "../../../ai/AzureAIIntegrationService";
import { GatewayService } from "../../../services/gatewayService";

const aiService = new AIService();
const caseService = new CaseService(aiService);
const chatService = new ChatService(caseService);

export class ChatController {
  async handleWebhook(event: APIGatewayProxyEvent, context: Context): Promise<APIGatewayProxyResult> {
    try {
      // Webhook verification (GET)
      if (event.queryStringParameters) {
        console.log("[CHAT][CONTROLLER] Processing webhook verification");
        const queryParams = event.queryStringParameters;
        const verify_token = process.env.VERIFY_TOKEN || "SECRET_TOKEN";

        let mode = queryParams["hub.mode"];
        let token = queryParams["hub.verify_token"];
        let challenge = queryParams["hub.challenge"];

        console.log("[CHAT_CONTROLLER] Webhook verification params:", {
          mode,
          tokenProvided: !!token,
          hasChallenge: !!challenge,
        });

        if (mode && token) {
          if (mode === "subscribe" && token === verify_token) {
            console.log("[CHAT_CONTROLLER]Webhook verified successfully");
            return {
              statusCode: 200,
              body: challenge || "",
            };
          } else {
            console.warn("[CHAT_CONTROLLER]Webhook verification failed:", {
              mode,
              tokenMatch: token === verify_token,
            });
            return {
              statusCode: 403,
              body: "",
            };
          }
        }
        console.warn("[CHAT_CONTROLLER]Invalid webhook verification request");
        return {
          statusCode: 400,
          headers: {
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Headers": "*",
          },
          body: JSON.stringify({ message: "Invalid request" }),
        };
      }

      // Process body (POST)
      if (!event.body) {
        console.warn("Missing request body");
        return {
          statusCode: 400,
          body: JSON.stringify({ message: "Missing body" }),
        };
      }
      console.log("[CHAT_CONTROLLER] Webhook event body:", event.body);

      const body = JSON.parse(event.body);
      console.log("[CHAT_CONTROLLER] Parsed webhook body:", {
        object: body.object,
        hasEntry: !!body.entry,
        entryCount: body.entry?.length,
      });

      // Outbound message
      if (body.messageDirection === MessageDirection.OUTBOUND) {
        console.log(
          `[CHAT_CONTROLLER][OUTBOUND] tid=${body.messageId} Processing message:`,
          {
            userId: body.userId,
            messageType: body.messageType,
            messagePlatform: body.messagePlatform,
          }
        );
        await chatService.sendMessage(body);
        return {
          statusCode: 200,
          body: JSON.stringify({
            message: "Message sent successfully",
          }),
        };
      }

      // Determine message source
      const source =
        body.object === "whatsapp_business_account"
          ? MessagePlatform.WHATSAPP
          : MessagePlatform.NATIVE;
      console.log("[CHAT_CONTROLLER] Message source:", source);

      // WhatsApp status update
      if (source === MessagePlatform.WHATSAPP) {
        if (body.entry?.[0].changes?.[0].value.statuses) {
          const status = body.entry[0].changes[0].value.statuses[0];
          console.log(
            `[CHAT_CONTROLLER][STATUS_UPDATE] tid=${status.id} Processing message status:`,
            {
              status: status.status,
              timestamp: status.timestamp,
            }
          );
          return {
            statusCode: 200,
            body: JSON.stringify({
              message: "Status update processed successfully",
            }),
          };
        }
      }

      // Inbound message
      if (source && body.entry?.[0].changes?.[0].value.messages) {
        console.log(
          `[CHAT_CONTROLLER] [INBOUND] Processing ${source} message -> ${JSON.stringify(
            body,
            null,
            2
          )}`
        );
        try {
          const message = body.entry[0].changes[0].value.messages[0];
          const contact = body.entry[0].changes[0].value.contacts[0];

          if (message?.type.toUpperCase() === "IMAGE") {
            console.log(
              `[CHAT_CONTROLLER][INBOUND] tid=${message.id} Processing Image Message`
            );
            const imageUrl = await chatService.processImageUrl(source, message);
            console.log(
              `[CHAT_CONTROLLER][INBOUND] tid=${message.id} Image URL:`,
              imageUrl
            );
            message[message.type].url = imageUrl;
            console.log(
              `[CHAT_CONTROLLER][INBOUND] tid=${message.id} Updated Image URL:`,
              message[message.type].url
            );
          }

          console.log(
            `[CHAT_CONTROLLER][INBOUND] tid=${message.id} Message details:`,
            {
              messageType: message.type,
              userId: contact.wa_id,
              messagePlatform: source,
              userFullName: contact.profile.name,
              timestamp: new Date().toISOString(),
            }
          );

          // Directly call GatewayService instead of HTTP
          const gatewayService = new GatewayService();

          // Step 1: Check/Create user with all required fields
          await gatewayService.getOrCreateUser(
            contact.wa_id,
            contact.profile.name,
            contact.wa_id,
            message.id
          );

          // Step 2: Check for existing open case
          let activeCase = await gatewayService.getActiveCase(contact.wa_id, message.id);
          let caseId = activeCase?.caseId;
          if (!caseId) {
            caseId = await gatewayService.createNewCase(contact.wa_id, message.id);
            caseId = caseId?.caseId || caseId; // handle both object and string
          }

          // Step 3: Store the message
          await gatewayService.storeMessage({
            caseId,
            userId: contact.wa_id,
            messageId: message.id,
            messageType: message.type.toUpperCase(),
            messageDirection: "INBOUND",
            messageContent: message[message.type],
            messagePlatform: source,
            messaageOrigin: MessageOrigin.USER,
            messageContext: message?.context,
          });

          return {
            statusCode: 200,
            body: JSON.stringify({
              message: "Message processed successfully",
            }),
          };
        } catch (error) {
          console.error(
            `[CHAT_CONTROLLER][INBOUND_ERROR] tid=${body.entry?.[0]?.changes?.[0]?.value?.messages?.[0]?.id} Error Processing ${source} Message:`,
            {
              error,
              errorType:
                error instanceof Error ? error.constructor.name : "Unknown",
              errorMessage:
                error instanceof Error ? error.message : "Unknown error",
              timestamp: new Date().toISOString(),
            }
          );
          if (source === MessagePlatform.NATIVE) {
            return {
              statusCode: 202, // Accepted but processing may fail
              body: JSON.stringify({
                message: "Message received, processing failed",
                error: error instanceof Error ? error.message : "Unknown error",
              }),
            };
          }
          throw error;
        }
      }
      console.log(
        "[CHAT_CONTROLLER] Request processing completed with no matching conditions"
      );
      return {
        statusCode: 400,
        body: JSON.stringify({ message: "Invalid request" }),
      };
    } catch (error) {
      console.error("[CHAT_CONTROLLER_ERROR] Unhandled Error:", {
        error,
        errorType: error instanceof Error ? error.constructor.name : "Unknown",
        errorMessage: error instanceof Error ? error.message : "Unknown error",
        requestId: context.awsRequestId,
        timestamp: new Date().toISOString(),
      });
      return {
        statusCode: 500,
        body: JSON.stringify({ message: "[CHAT_CONTROLLER] Internal Server Error" }),
      };
    }
  }
} 