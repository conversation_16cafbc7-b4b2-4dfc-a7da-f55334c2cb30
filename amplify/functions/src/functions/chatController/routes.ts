import { APIGatewayProxyEvent, APIGatewayProxyResult, Context } from "aws-lambda";
import { ChatController } from "./controllers/chatController";

const chatController = new ChatController();

export const router = async (
  event: APIGatewayProxyEvent,
  context: Context
): Promise<APIGatewayProxyResult> => {
  const path = event.path.toLowerCase();
  const method = event.httpMethod.toUpperCase();

  if (path === "/chat/webhook" && (method === "POST" || method === "GET")) {
    return await chatController.handleWebhook(event, context);
  }

  return {
    statusCode: 404,
    body: JSON.stringify({ message: "Route not found" })
  };
}; 