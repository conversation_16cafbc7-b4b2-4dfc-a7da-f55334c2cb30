# Monova AI Stylist - WebSocket API

## Overview

This project is part of Monova, an AI-powered fashion styling application built with AWS Amplify (React). The WebSocket API provides real-time bidirectional communication between clients and the Monova backend, enabling instant delivery of styling recommendations, outfit feedback, and chat messages to users across multiple platforms.

The API consists of four main Lambda functions that handle different aspects of WebSocket communication:
- Connection establishment and authentication
- Message processing and routing
- Real-time outbound notifications
- Connection termination and cleanup

## Project Structure

The WebSocket API follows AWS Amplify's serverless architecture, utilizing API Gateway WebSockets and DynamoDB for connection tracking:

- **Controllers**
  - `connectSocketController`: Handles new WebSocket connections and authentication
  - `disconnectSocketController`: Manages WebSocket disconnection and cleanup
  - `inboundSocketController`: Processes messages from clients to the backend
  - `outboundSocketController`: Sends messages from the backend to connected clients

- **Service Layer**
  - `socketService.ts`: Business logic for WebSocket operations
  
- **Data Access Layer**
  - `socketDao.ts`: Interface to DynamoDB for connection tracking

## WebSocket Flow

### 1. Connection Establishment

When a client attempts to connect:

1. <PERSON><PERSON> initiates a WebSocket connection with query parameters:
   - `userId`: Unique identifier for the user
   - `authToken`: JWT authentication token

2. `connectSocketController` validates the request:
   - Verifies required parameters are present
   - Validates the authentication token
   - Checks for existing connections for the same user

3. Connection information is stored in DynamoDB:
   - If a connection already exists for the user, it is updated
   - Otherwise, a new connection record is created

4. On successful connection, the client can begin sending and receiving messages

### 2. Message Processing

#### Inbound Messages (Client to Server)

1. Client sends a message through the established WebSocket connection

2. `inboundSocketController` processes the message:
   - Validates the message format
   - Routes the message to the appropriate backend service

3. The message is forwarded to the Chat API for processing:
   - Content is parsed and validated
   - Business logic is applied (conversation handling, styling advice, etc.)
   - Response is generated and may trigger outbound messages

#### Outbound Messages (Server to Client)

1. Backend services generate messages to be sent to clients

2. `outboundSocketController` receives the outbound message request:
   - Message includes the target user ID (`to` field)
   - Connection ID is retrieved from DynamoDB using the user ID

3. API Gateway Management API is used to send the message to the specific client connection

4. Client receives the message in real-time through the WebSocket connection

### 3. Disconnection

When a client disconnects:

1. `disconnectSocketController` is triggered automatically by API Gateway

2. The function performs cleanup:
   - Removes the connection record from DynamoDB
   - Logs the disconnection event

## Authentication

The WebSocket API implements a secure authentication flow:

1. **Token Verification**: 
   - Authentication tokens are validated through the Auth Service
   - Only authenticated users can establish connections

2. **Connection Management**:
   - Each user can have only one active connection (latest connection is preserved)
   - Connections are tied to specific user IDs

## Connection Storage

User connection information is stored in DynamoDB with the following structure:

- `userId` (Primary Key): Unique identifier for the user
- `connectionId`: WebSocket connection ID assigned by API Gateway
- `createdAt`: Timestamp when the connection was established

This allows the system to:
- Track all active connections
- Map user IDs to connection IDs for targeted messaging
- Maintain connection state across backend services

## Integration with Monova Services

The WebSocket API integrates with other Monova services:

1. **Chat API**: 
   - Receives inbound messages from the WebSocket for processing
   - Generates responses that are sent back through WebSockets

2. **Auth Service**:
   - Verifies user authentication tokens
   - Ensures secure communication

3. **Notification System**:
   - Delivers real-time notifications to users
   - Provides updates on styling recommendations and outfit feedback

## Error Handling

The WebSocket API implements comprehensive error handling:

1. **Connection Errors**:
   - Missing parameters trigger appropriate error responses
   - Authentication failures are handled gracefully
   - Connection limits are enforced

2. **Message Processing Errors**:
   - Invalid message formats are detected and logged
   - Processing failures don't disrupt the connection
   - Errors are reported back to clients when appropriate

3. **Disconnection Errors**:
   - Graceful handling of unexpected disconnections
   - Resource cleanup even in failure scenarios

## Example Scenarios

### Real-Time Outfit Recommendations

1. User requests an outfit recommendation through the mobile app
2. Request is processed by the MAIS Controller
3. When the outfit is generated, it's immediately pushed to the user through WebSocket
4. User receives the recommendation without needing to refresh or poll

### Interactive Styling Sessions

1. User uploads an outfit photo for feedback
2. AI analyzes the image and generates recommendations
3. Feedback is delivered in real-time through WebSocket
4. User can respond immediately, creating a conversational experience

### Multi-Device Synchronization

1. User is logged into multiple devices (mobile, web)
2. Each device maintains a WebSocket connection
3. Actions on one device are reflected on all others in real-time
4. Profile updates, recommendations, and chat history stay synchronized

## Implementation Details

### Connect Handler

The connect handler validates connection requests and stores connection information:

```javascript
// Main flow
1. Extract connectionId and query parameters
2. Validate userId and authToken presence
3. Verify authentication token with Auth Service
4. Check for existing connections for the user
5. Create or update connection record in DynamoDB
6. Return success response
```

### Inbound Message Handler

The inbound handler processes messages from clients:

```javascript
// Main flow
1. Extract connectionId and message body
2. Validate message format
3. Forward message to Chat API for processing
4. Return acknowledgment response
```

### Outbound Message Handler

The outbound handler sends messages to connected clients:

```javascript
// Main flow
1. Extract target userId from request
2. Retrieve connectionId from DynamoDB
3. Format message for delivery
4. Use API Gateway Management API to send message
5. Return success response
```

### Disconnect Handler

The disconnect handler cleans up when connections are terminated:

```javascript
// Main flow
1. Extract connectionId
2. Remove connection record from DynamoDB
3. Log disconnection event
4. Return success response
```

## Security Considerations

The WebSocket API implements several security measures:

1. **Authentication**: 
   - JWT token validation
   - Token expiration and signature verification

2. **Authorization**:
   - Users can only access their own connections
   - Backend services are authorized to send messages to any user

3. **Connection Limits**:
   - Each user is limited to one active connection
   - Rate limiting on connection attempts

4. **Message Validation**:
   - All messages are validated before processing
   - Malformed messages are rejected

## Future Enhancements

Potential improvements to consider:

1. **Connection Monitoring**:
   - Add heartbeat mechanism to detect stale connections
   - Implement automatic reconnection strategies

2. **Advanced Routing**:
   - Implement message routing based on topics or channels
   - Support for selective message delivery

3. **Scalability Improvements**:
   - Connection pooling for high-volume scenarios
   - Regional distribution for global users

4. **Enhanced Security**:
   - Message encryption for sensitive data
   - Advanced token refresh mechanisms

5. **Operational Features**:
   - Connection analytics and metrics
   - Debug modes for troubleshooting
