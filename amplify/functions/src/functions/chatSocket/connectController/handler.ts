import { APIGatewayProxyWebsocketHandlerV2 } from "aws-lambda";
import { SocketService } from "../../../services/socketService";
import { Amplify } from "aws-amplify";
import outputs from "../../../../../../amplify_outputs.json";
Amplify.configure(outputs);

const socketService = new SocketService();

export const handler: APIGatewayProxyWebsocketHandlerV2 = async (event) => {
  const connectionId = event.requestContext.connectionId;
  const queryParams = (event as any).queryStringParameters;

  console.log(`[WEBSOCKET][CONNECT] Event:`, {
    connectionId,
    queryParams,
    timestamp: new Date().toISOString(),
  });

  try {
    const userId = queryParams?.userId;
    const authToken = queryParams?.authToken;
    
    if (!userId) {
      console.error(
        `[WEBSOCKET][CONNECT] User Id not found for connectionId: ${connectionId}`
      );
      return {
        statusCode: 400,
        body: JSON.stringify({
          message: `User Id not found for connectionId: ${connectionId}`,
        }),
      };
    }

    if (!authToken) {
      console.error(
        `[WEBSOCKET][CONNECT] Auth Token not found for connectionId: ${connectionId}`
      );
      return {
        statusCode: 403,
        body: JSON.stringify({
          message: `Auth Token not found for connectionId: ${connectionId}`,
        }),
      };
    }

    // Verify Auth token
    const response = await socketService.verifyAuthToken(userId, authToken);
    console.log(
      `[WEBSOCKET][CONNECT] Auth Verification Response for ${userId} `,
      response
    );

    if (!response) {
      console.error(
        `[WEBSOCKET][CONNECT] Invalid Auth Token for connectionId: ${connectionId}`
      );
      return {
        statusCode: 401,
        body: JSON.stringify({
          message: `Invalid Auth Token for connectionId: ${connectionId}`,
        }),
      };
    }

    // Check if connection already exists
    const existingConnection = await socketService.getConnection(userId);
    if (existingConnection?.data) {
      console.warn([
        `WEBSOCKET][CONNECT] Connection already exists for user: ${userId}`,
      ]);
      console.log(`[WEBSOCKET][CONNECT] Updating connection:`, {
        connectionId,
        userId,
      });
      await socketService.updateConnection(
        userId,
        connectionId,
        Date.now().toString()
      );
    } else {
      console.log(`[WEBSOCKET][CONNECT] Creating connection:`, {
        connectionId,
        userId,
      });
      await socketService.createConnection(
        userId,
        connectionId,
        Date.now().toString()
      );
    }

    console.log(`[WEBSOCKET][CONNECT] Connection established:`, {
      connectionId,
      userId,
    });
    
    return {
      statusCode: 200,
      body: JSON.stringify({ message: "Connected successfully" }),
    };
  } catch (error) {
    console.error(`[WEBSOCKET][CONNECT] Error:`, { error, connectionId });
    return {
      statusCode: 500,
      body: JSON.stringify({ message: `Connection error: ${error}` }),
    };
  }
};
