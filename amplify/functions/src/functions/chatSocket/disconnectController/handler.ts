// functions/websocketDisconnect/handler.ts
import { APIGatewayProxyWebsocketHandlerV2 } from "aws-lambda";
import { SocketService } from "../../../services/socketService";
import { Amplify } from "aws-amplify";
import outputs from "../../../../../../amplify_outputs.json";

Amplify.configure(outputs);

export const handler: APIGatewayProxyWebsocketHandlerV2 = async (event) => {
  const connectionId = event.requestContext.connectionId;
  const socketService = new SocketService();
  const queryParams = (event as any).queryStringParameters;

  console.log(`[WEBSOCKET][DISCONNECT] Event:`, {
    connectionId,
    timestamp: new Date().toISOString(),
  });

  try {
    console.log(`[WEBSOCKET][DISCONNECT] Deleting connection:`, {
      connectionId,
    });

    // NOTE : NO QUERY PARRAMS ARE PRESERNT  WHILE DISCONECTING THE SOCKET
    // SO THE CONNECTION ID WILL JUST BE UPDATED WHILE CONNECTING THE SOCKET
    if (!queryParams?.userId) {
      console.warn(
        `[WEBSOCKET][DISCONNECT] User Id not found for connectionId: ${connectionId}`
      );
      return {
        statusCode: 200,
        body: JSON.stringify({ message: "Unable to Delete Connection Record" }),
      };
    }

    console.log(
      `[WEBSOCKET][DISCONNECT] Deleting connection for userId:`,
      queryParams.userId
    );
    
    await socketService.deleteConnection(queryParams.userId);
    console.log(`[WEBSOCKET][DISCONNECT] Connection deleted successfully`);

    return {
      statusCode: 200,
      body: JSON.stringify({ message: "Disconnected successfully" }),
    };
  } catch (error) {
    console.error(`[WEBSOCKET][DISCONNECT] Error:`, { error, connectionId });
    return {
      statusCode: 500,
      body: JSON.stringify({ message: `Disconnect error: ${error}` }),
    };
  }
};
