import { APIGatewayProxyWebsocketHandlerV2 } from "aws-lambda";
import { SocketService } from "../../../services/socketService";
import { Amplify } from "aws-amplify";
import outputs from "../../../../../../amplify_outputs.json";
Amplify.configure(outputs);

const socketService = new SocketService();
export const handler: APIGatewayProxyWebsocketHandlerV2 = async (event) => {
  const connectionId = event.requestContext.connectionId;
  const requestTimeEpoch = event.requestContext.requestTimeEpoch;

  console.log("[WEBSOCKET][INBOUND] Event :", {
    connectionId,
    requestTimeEpoch,
    TimeStamp: new Date(requestTimeEpoch),
  });

  if (!event?.body) {
    console.log("[WEBSOCKET][INBOUND] No body found in event");
    return {
      statusCode: 400,
      body: JSON.stringify({
        message: "No body found in event",
      }),
    };
  }

  console.log("[WEBSOCKET][INBOUND] Event Body :", event.body);

  try {
    console.log("[WEBSOCKET][INBOUND] Sending message to /chat");
    const result = await socketService.sendMessage(event.body);
    console.log("[WEBSOCKET][INBOUND] Message Sent Successfully :", result);
    return {
      statusCode: 200,
      body: JSON.stringify({
        message: "Message Sent Successfully",
      }),
    };
  } catch (error) {
    console.error("[WEBSOCKET][INBOUND] Error :", error);
    return {
      statusCode: 500,
      body: JSON.stringify({
        message: "Internal Server Error",
      }),
    };
  }
};
