import type { APIGatewayProxyHandler } from "aws-lambda";
import {
  ApiGatewayManagementApiClient,
  PostToConnectionCommand,
} from "@aws-sdk/client-apigatewaymanagementapi";
import { Amplify } from "aws-amplify";
import outputs from "../../../../../../amplify_outputs.json";
import { SocketService } from "../../../services/socketService";

Amplify.configure(outputs);

const domain = outputs.custom.API.chatSocketApi_TEST_ANKUR.domain.replace(
  "wss://",
  "https://"
);
const stage = outputs.custom.API.chatSocketApi_TEST_ANKUR.stage;
const callbackUrl = `${domain}/${stage}`;
const client = new ApiGatewayManagementApiClient({
  endpoint: callbackUrl,
  region: outputs.custom.API.chatSocketApi_TEST_ANKUR.region,
});
const socketService = new SocketService();

export const handler: APIGatewayProxyHandler = async (event, context) => {
  console.log("[CHAT_HANDLER] === Starting Chat Controller Handler ===", {
    requestId: context.awsRequestId,
    timestamp: new Date().toISOString(),
  });
  try {
    console.log("[OUTBOUND] === Event ===", event);
    // Fethcing UserId from event
    if (!event.body) {
      throw new Error("Invalid Request :  No Body Found");
    }

    const body = JSON.parse(event.body);
    const userId = body.to;
    if (!userId) {
      throw new Error("Invalid Request : No UserId Found");
    }

    // Fetching ConnectionId from userId
    console.log("[OUTBOUND] === Fetching ConnectionId ===", userId);
    const connectionData = await socketService.getConnection(userId);

    console.log("[OUTBOUND] === Connection Data ===", connectionData);
    const connectionId = connectionData.data?.connectionId;
    if (!connectionId) {
      throw new Error("Connection Id not found");
    }

    const requestParams = {
      ConnectionId: connectionId,
      Data: JSON.stringify(body),
    };


    // const requestParams = {
    //   ConnectionId: connectionId,
    //   Data: JSON.stringify({
    //     message: "Sending Internal Servier Error For Testing Purposes We will be back soon!",
    //     error: "Internal Server Error",
    //   }),
    // };

    console.log("[OUTBOUND] === Request Params ===", requestParams);

    // Sending message to the connectionId
    const command = new PostToConnectionCommand(requestParams);
    const res = await client.send(command);

    console.log("[OUTBOUND] === Response ===", res);

    return {
      statusCode: 200,
      body: JSON.stringify({
        message: "Message sent successfully",
        data: requestParams,
      }),
    };
  } catch (error) {
    console.error("[OUTBOUND] === Error ===", error);
    return {
      statusCode: 500,
      body: JSON.stringify({
        message: "Internal Server Error",
        error: error,
      }),
    };
  }
};
