import { APIGatewayProxyEvent, APIGatewayProxyResult } from "aws-lambda";
import { GatewayService } from "../../../services/gatewayService";
import { MAISService } from "../../../services/maisService";
import { MessageOrigin } from "../../../config/constants";

export class GatewayController {
  private gatewayService: GatewayService;

  constructor() {
    this.gatewayService = new GatewayService();
  }

  async handleMessage(event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> {
    try {
      // Parse the incoming message from the request body
      const body = JSON.parse(event.body || "{}");
      const messageId = body.messageId;
      console.log(
        `[GatewayController][handleMessage] tid=${messageId} received request, body-> ${JSON.stringify(
          body,
          null,
          2
        )}`
      );

      const {
        userId,
        userFullName,
        userWhatsAppNumber,
        messageType,
        messageDirection,
        messageContent,
        messageContext,
        messagePlatform,
      } = body;

      // Validate required fields
      if (
        !userId ||
        !userFullName ||
        !userWhatsAppNumber ||
        !messageId ||
        !messageType ||
        !messageDirection ||
        !messageContent ||
        !messagePlatform
      ) {
        console.warn(
          `[GatewayController][handleMessage] tid=${messageId} Missing required fields`
        );
        return {
          statusCode: 400,
          body: JSON.stringify({
            message: "Missing required fields",
          }),
        };
      }

      // Step 1: Check/Create user with all required fields
      const userInfo = await this.gatewayService.getOrCreateUser(
        userId,
        userFullName,
        userWhatsAppNumber,
        messageId
      );

      console.log(
        `[GatewayController][handleMessage] tid=${messageId} User info:`,
        userInfo
      );

      // Step 2: Check for existing open case
      const activeCase = await this.gatewayService.getActiveCase(userId, messageId);

      // Create new case or use existing one
      let caseId = activeCase?.caseId;

      if (!caseId) {
        caseId = await this.gatewayService.createNewCase(userId, messageId);
      }

      // Store the message with all required fields
      await this.gatewayService.storeMessage({
        caseId,
        userId,
        messageId,
        messageType,
        messageDirection,
        messageContent,
        messagePlatform,
        messaageOrigin: MessageOrigin.USER,
        messageContext,
      });

      // Step 3: Process the message through MAIS service directly
      console.log(
        `[GatewayController][handleMessage] tid=${messageId} Calling MAISService.processMessage directly:`,
        {
          caseId,
          userId,
          messageId,
        }
      );

      try {
        const maisService = new MAISService();
        const maisResult = await maisService.processMessage(
          caseId,
          userId,
          messageId,
          messagePlatform
        );

        console.log(
          `[GatewayController][handleMessage] tid=${messageId} MAISService.processMessage result:`,
          maisResult
        );

        return {
          statusCode: 200,
          body: JSON.stringify({
            message: "Message processed successfully",
            caseId,
            maissResult: maisResult,
          }),
        };
      } catch (error) {
        console.error(
          `[GatewayController][handleMessage] tid=${messageId} Error calling MAISService.processMessage:`,
          {
            error,
            caseId,
            userId,
            messageId,
            errorMessage:
              error instanceof Error ? error.message : "Unknown error",
          }
        );

        return {
          statusCode: 500,
          body: JSON.stringify({
            message: "Message MAIS processing failed",
            caseId,
            error: error instanceof Error ? error.message : "Unknown error",
          }),
        };
      }
    } catch (error) {
      console.error(`[GatewayController][handleMessage] Unhandled Error :`, error);
      return {
        statusCode: 500,
        body: JSON.stringify({
          message: "Internal server error",
          error: error instanceof Error ? error.message : "Unknown error",
        }),
      };
    }
  }
} 