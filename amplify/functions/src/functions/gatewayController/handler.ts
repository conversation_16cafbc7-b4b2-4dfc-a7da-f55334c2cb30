import { APIGatewayProxyHand<PERSON>, APIGatewayProxyResult } from "aws-lambda";
import { router } from "./routes";

console.log(`[GATEWAY] Initialized Controller`);

export const handler: APIGatewayProxyHandler = async (
  event
): Promise<APIGatewayProxyResult> => {
  console.log("Request received:", {
    path: event.path,
    method: event.httpMethod,
    body: event.body,
  });

  try {
    return await router(event);
  } catch (error) {
    console.error("Error in handler:", error);
    return {
      statusCode: 500,
      body: JSON.stringify({ message: "Internal server error" }),
    };
  }
};
