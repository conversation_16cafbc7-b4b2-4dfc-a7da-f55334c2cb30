import { APIGatewayProxyEvent, APIGatewayProxyResult } from "aws-lambda";
import { GatewayController } from "./controllers/gatewayController";

const gatewayController = new GatewayController();

export const router = async (
  event: APIGatewayProxyEvent
): Promise<APIGatewayProxyResult> => {
  const path = event.path.toLowerCase();
  const method = event.httpMethod.toUpperCase();

  if (path === "/gateway/message" && method === "POST") {
    return await gatewayController.handleMessage(event);
  }

  return {
    statusCode: 404,
    body: JSON.stringify({ message: "Route not found" })
  };
}; 