import { APIGatewayProxyEvent, APIGatewayProxyResult } from "aws-lambda";
import { MAISService } from "../../../services/maisService";

export class MAISController {
  private maisService: MAISService;

  constructor() {
    this.maisService = new MAISService();
  }

  async handleMessage(event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> {
    try {
      if (event.httpMethod !== "POST") {
        return {
          statusCode: 405,
          body: JSON.stringify({ message: "Method not allowed" }),
        };
      }

      const payload = JSON.parse(event.body || "{}");
      const { caseId, userId, messageId, messagePlatform } = payload;

      // Validate required fields
      if (!caseId || !userId || !messageId || !messagePlatform ) {
        return {
          statusCode: 400,
          body: JSON.stringify({
            message:
              "Missing required fields: caseId, userId, and messageId are required",
          }),
        };
      }

      // Process the message
      const result = await this.maisService.processMessage(
        caseId,
        userId,
        messageId,
        messagePlatform
      );

      return {
        statusCode: 200,
        body: JSON.stringify({
          message: "Message processed successfully",
          data: result,
        }),
      };
    } catch (error) {
      console.error("[MAISController] Error processing message:", error);
      return {
        statusCode: 500,
        body: JSON.stringify({
          message: "Internal server error",
          error: error instanceof Error ? error.message : "Unknown error",
        }),
      };
    }
  }
} 