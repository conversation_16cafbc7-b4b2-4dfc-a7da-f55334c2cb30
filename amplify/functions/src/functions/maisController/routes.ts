import { APIGatewayProxyEvent, APIGatewayProxyResult } from "aws-lambda";
import { MAISController } from "./controllers/maisController";

const maisController = new MAISController();

export const router = async (
  event: APIGatewayProxyEvent
): Promise<APIGatewayProxyResult> => {
  const path = event.path.toLowerCase();
  const method = event.httpMethod.toUpperCase();

  if (path === "/mais/message" && method === "POST") {
    return await maisController.handleMessage(event);
  }

  return {
    statusCode: 404,
    body: JSON.stringify({ message: "Route not found" })
  };
}; 