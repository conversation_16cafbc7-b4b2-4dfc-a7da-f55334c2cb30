import type { DynamoDBStreamEvent } from "aws-lambda";
import { Amplify } from "aws-amplify";
import axios from "axios";
import outputs from "../../../../../amplify_outputs.json";
Amplify.configure(outputs);

console.log("[OTP_SENDER] Loading function...");
console.log("[OTP_SENDER] Loading Amplify configuration...");

const DEFAULT_ADMIN_IDS = ["917528826015", "918837671870", "919999988888"];

const envAdminIds = process.env.ADMIN_PHONE_NUMBERS?.split(",").filter(Boolean);
const adminIds = envAdminIds?.length ? envAdminIds : DEFAULT_ADMIN_IDS;


const SMS_API_BASE_URL = "https://api.authkey.io/request";
const AUTH_KEY = process.env.AUTH_KEY || "864c013b495c69b6"; // Should be in environment variables
const SID = "16026";

export const handler = async (event: DynamoDBStreamEvent) => {
  try {
    console.log("[OTP_SENDER] Processing records:", {
      count: event.Records.length,
      timestamp: new Date().toISOString(),
    });

    for (const record of event.Records) {
      if (record.eventName === "INSERT") {
        const userId = record.dynamodb?.NewImage?.userId?.S;
        const otp = record.dynamodb?.NewImage?.otp?.S;

        console.log("[OTP_SENDER] Processing record:", {
          userId,
          otpLength: otp?.length,
        });

        if (!userId || !otp) {
          console.error("[OTP_SENDER] Missing required fields:", {
            hasUserId: !!userId,
            hasOTP: !!otp,
            timestamp: new Date().toISOString(),
          });
          throw new Error(`[OTP_SENDER] Missing Required Fields \n 
                  userId: ${userId} \n
                  otp: ${otp}`);
        }

        if (adminIds.includes(userId)) {
          console.log("[OTP_SENDER] Skipping OTP send for admin user:", userId);
          continue;
        }

        // Sanitize phone number - remove spaces and any non-numeric characters

        console.log("[OTP_SENDER] Sending OTP request:", {
          userId: userId,
          timestamp: new Date().toISOString(),
        });

        const mobile = userId.length === 12 ? userId.slice(2) : userId;
        const country_code = userId.length === 12 ? userId.slice(0, 2) : "91";

        console.log("[OTP_SENDER] Sending OTP request:", {
          userId: userId,
          mobile: mobile,
          country_code: country_code,
          timestamp: new Date().toISOString(),
          AUTH_KEY: AUTH_KEY,
          SID: SID,
        });

        try {
          // Prepare request data
          const response = await axios.get(SMS_API_BASE_URL, {
            params: {
              authkey: AUTH_KEY,
              mobile: mobile,
              country_code: country_code,
              sid: SID,
              company: "Monova",
              otp: otp,
            },
          });

          console.log("[OTP_SENDER] SMS API Response:", {
            status: response.status,
            data: response.data,
            timestamp: new Date().toISOString(),
          });

          if (response.status !== 200 || !response.data.return) {
            throw new Error(
              `API returned unsuccessful status: ${JSON.stringify(
                response.data
              )}`
            );
          }
        } catch (apiError: any) {
          console.error("[OTP_SENDER] API Request Failed:", {
            error: apiError,
            errorType:
              apiError instanceof Error ? apiError.constructor.name : "Unknown",
            errorMessage:
              apiError instanceof Error ? apiError.message : "Unknown error",
            response: apiError.response?.data,
            status: apiError.response?.status,
            timestamp: new Date().toISOString(),
          });

          // For other errors, log but don't retry
          console.warn("[OTP_SENDER] error encountered:", {
            userId: userId,
            errorType: apiError.response?.status || apiError.code,
          });
        }
      }
    }
  } catch (error) {
    console.error("[OTP_SENDER_ERROR] Unhandled Error:", {
      error,
      errorType: error instanceof Error ? error.constructor.name : "Unknown",
      errorMessage: error instanceof Error ? error.message : "Unknown error",
      timestamp: new Date().toISOString(),
      stackTrace: error instanceof Error ? error.stack : undefined,
    });
    throw error;
  }
};
