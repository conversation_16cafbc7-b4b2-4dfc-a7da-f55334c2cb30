// handler.ts
import { OutfitService } from "../../services/outfitService";
import { AuthService } from "../../services/authService";
import type {
  APIGatewayProxyHandler,
  APIGatewayProxyEvent,
  APIGatewayProxyResult,
} from "aws-lambda";
import type {
  Outfit,
  CreateOutfitInput,
  UpdateOutfitInput,
  ListOutfitsInput,
  ListOutfitsOutput,
  UpdateOutfitOutput,
} from "./types";

// Custom error classes for better error handling
class ValidationError extends Error {
  constructor(message: string) {
    super(message);
    this.name = "ValidationError";
  }
}

class ResourceNotFoundError extends Error {
  constructor(message: string) {
    super(message);
    this.name = "ResourceNotFoundError";
  }
}

class AuthenticationError extends Error {
  constructor(message: string) {
    super(message);
    this.name = "AuthenticationError";
  }
}

// Initialize services
const outfitService = new OutfitService();
const authService = new AuthService();

// Response helper functions
const createResponse = (
  statusCode: number,
  body: any
): APIGatewayProxyResult => ({
  statusCode,
  headers: {
    "Content-Type": "application/json",
    "Access-Control-Allow-Origin": "*",
    "Access-Control-Allow-Headers": "Content-Type,Authorization",
    "Access-Control-Allow-Methods": "OPTIONS,POST,GET,PUT,DELETE",
  },
  body: JSON.stringify(body),
});

const handleError = (error: unknown): APIGatewayProxyResult => {
  console.error("[OutfitController] Error:", error);

  if (error instanceof ValidationError) {
    return createResponse(400, {
      error: "Validation Error",
      message: error.message,
    });
  }

  if (error instanceof ResourceNotFoundError) {
    return createResponse(404, {
      error: "Not Found",
      message: error.message,
    });
  }

  if (error instanceof AuthenticationError) {
    return createResponse(401, {
      error: "Authentication Error",
      message: error.message,
    });
  }

  if (error instanceof Error && error.message === "Outfit not found") {
    return createResponse(404, {
      error: "Not Found",
      message: error.message,
    });
  }

  return createResponse(500, {
    error: "Internal Server Error",
    message:
      error instanceof Error ? error.message : "An unexpected error occurred",
  });
};

// Helper functions
const validateCreateInput = (
  input: Partial<CreateOutfitInput>
): CreateOutfitInput => {
  if (
    !input.userId ||
    !input.outfitImage ||
    !input.topWear ||
    !input.bottomWear ||
    !input.footWear ||
    !input.accessories
  ) {
    throw new ValidationError("Missing required fields in create outfit input");
  }
  return input as CreateOutfitInput;
};

const validateUserId = (userId: string | undefined): string => {
  if (!userId) {
    throw new ValidationError("userId is required");
  }
  return userId;
};

// Token verification middleware
const verifyToken = async (event: APIGatewayProxyEvent): Promise<void> => {
  const authHeader = event.headers.Authorization || event.headers.authorization;

  if (!authHeader?.startsWith("Bearer ")) {
    throw new AuthenticationError("Authorization header missing or invalid");
  }

  const token = authHeader.split(" ")[1];
  const userId =
    event.queryStringParameters?.userId ||
    (event.body ? JSON.parse(event.body).userId : null);

  if (!userId) {
    throw new ValidationError("userId is required for authentication");
  }

  try {
    const verification = await authService.verifyAuthToken(token);
    if (!verification.success) {
      throw new AuthenticationError("Invalid or expired token");
    }
    if (verification.user.userId !== userId) {
      throw new AuthenticationError("User ID mismatch with token");
    }
  } catch (error: any) {
    console.error("[OutfitController] Auth Error:", error);
    if (error.message === "Token expired") {
      throw new AuthenticationError("Invalid or expired token");
    }
    if (error.message === "User not found") {
      throw new AuthenticationError("User not found");
    }
    throw error;
  }
};

export const handler: APIGatewayProxyHandler = async (event) => {
  console.log(
    "[OutfitController] Received event:",
    JSON.stringify(event, null, 2)
  );

  try {
    const { httpMethod, path } = event;

    // Skip token verification for OPTIONS requests
    if (httpMethod !== "OPTIONS") {
      await verifyToken(event);
    }

    // Handle OPTIONS request for CORS
    if (httpMethod === "OPTIONS") {
      return createResponse(200, {});
    }

    const { queryStringParameters, body } = event;

    // Create outfit
    if (httpMethod === "POST" && path === "/outfit/create") {
      const input = validateCreateInput(JSON.parse(body || "{}"));
      const result: Outfit = await outfitService.createOutfit(input);
      return createResponse(201, result);
    }

    // Get outfit by ID
    if (httpMethod === "GET" && path === "/outfit/get") {
      const userId = validateUserId(queryStringParameters?.userId);
      const outfitId = queryStringParameters?.outfitId;

      if (!outfitId) {
        throw new ValidationError("outfitId is required");
      }

      try {
        const result: Outfit = await outfitService.getOutfitById(
          userId,
          outfitId
        );
        return createResponse(200, result);
      } catch (error) {
        if (error instanceof Error && error.message === "Outfit not found") {
          throw new ResourceNotFoundError(
            `Outfit not found with id: ${outfitId}`
          );
        }
        throw error;
      }
    }

    // List outfits
    if (httpMethod === "GET" && path === "/outfit/list") {
      const userId = validateUserId(queryStringParameters?.userId);
      const { limit, startKey } = queryStringParameters || {};

      const listInput: ListOutfitsInput = {
        userId,
        ...(limit && { limit: parseInt(limit) }),
        ...(startKey && { startKey: JSON.parse(decodeURIComponent(startKey)) }),
      };

      const result: ListOutfitsOutput = await outfitService.listOutfits(
        listInput
      );
      return createResponse(200, result);
    }

    // Update outfit
    if (httpMethod === "PUT" && path === "/outfit/update") {
      const userId = validateUserId(queryStringParameters?.userId);
      const outfitId = queryStringParameters?.outfitId;

      if (!outfitId) {
        throw new ValidationError("outfitId is required");
      }

      const updates: UpdateOutfitInput = JSON.parse(body || "{}");

      if (Object.keys(updates).length === 0) {
        throw new ValidationError("No update fields provided");
      }

      try {
        const result: UpdateOutfitOutput = await outfitService.updateOutfit(
          userId,
          outfitId,
          updates
        );
        return createResponse(200, result);
      } catch (error) {
        if (error instanceof Error && error.message === "Outfit not found") {
          throw new ResourceNotFoundError(
            `Outfit not found with id: ${outfitId}`
          );
        }
        throw error;
      }
    }

    // Delete outfit
    if (httpMethod === "DELETE" && path === "/outfit/delete") {
      const userId = validateUserId(queryStringParameters?.userId);
      const outfitId = queryStringParameters?.outfitId;

      if (!outfitId) {
        throw new ValidationError("outfitId is required");
      }

      try {
        await outfitService.deleteOutfit(userId, outfitId);
        return createResponse(204, null);
      } catch (error) {
        if (error instanceof Error && error.message === "Outfit not found") {
          throw new ResourceNotFoundError(
            `Outfit not found with id: ${outfitId}`
          );
        }
        throw error;
      }
    }

    // Handle unsupported routes
    return createResponse(404, {
      error: "Not Found",
      message: "Requested resource not found",
    });
  } catch (error) {
    return handleError(error);
  }
};
