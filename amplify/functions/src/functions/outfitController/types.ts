export interface BaseOutfit {
    userId: string;
    outfitId: string;
  }
  
  export interface Outfit extends BaseOutfit {
    outfitImage: string;
    topWear: string;
    bottomWear: string;
    footWear: string;
    accessories: string;
    createdAt: number;
    updatedAt: number;
  }
  
  export interface CreateOutfitInput {
    userId: string;
    outfitImage: string;
    topWear: string;
    bottomWear: string;
    footWear: string;
    accessories: string;
  }
  
  export interface UpdateOutfitInput {
    outfitImage?: string;
    topWear?: string;
    bottomWear?: string;
    footWear?: string;
    accessories?: string;
  }
  
  export interface UpdateOutfitOutput extends Partial<UpdateOutfitInput> {
    updatedAt: number;
  }
  
  export interface ListOutfitsInput {
    userId: string;
    limit?: number;
    startKey?: any;
  }
  
  export interface ListOutfitsOutput {
    outfits: Outfit[];
    lastEvaluatedKey?: Record<string, any> | undefined;
  }