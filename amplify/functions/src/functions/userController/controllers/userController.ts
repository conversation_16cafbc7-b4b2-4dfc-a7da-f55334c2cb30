import { APIGatewayProxyEvent, APIGatewayProxyResult, Context } from "aws-lambda";
import { UserService } from "../../../services/userService";

export class UserController {
  private userService: UserService;

  constructor() {
    this.userService = new UserService();
  }

  async createUser(event: APIGatewayProxyEvent, context: Context): Promise<APIGatewayProxyResult> {
    const body = JSON.parse(event.body || "{}");
    try {
      const user = await this.userService.createUser({
        userId: body.userId,
        userFullName: body.userFullName,
        userWhatsAppNumber: body.userWhatsAppNumber,
      });
      return {
        statusCode: 201,
        body: JSON.stringify(user),
      };
    } catch (error) {
      console.error("Error in createUser:", error);
      return {
        statusCode: 500,
        body: JSON.stringify({ message: "Internal server error" }),
      };
    }
  }

  async updateUser(event: APIGatewayProxyEvent, context: Context): Promise<APIGatewayProxyResult> {
    const body = JSON.parse(event.body || "{}");
    if (!body.userId) {
      return {
        statusCode: 400,
        body: JSON.stringify({ message: "userId is required" }),
      };
    }
    const updatedUser = await this.userService.updateUser(body.userId, body.updates);
    return {
      statusCode: 200,
      body: JSON.stringify(updatedUser),
    };
  }

  async getUser(event: APIGatewayProxyEvent, context: Context): Promise<APIGatewayProxyResult> {
    const body = JSON.parse(event.body || "{}");
    if (!body.userId) {
      return {
        statusCode: 400,
        body: JSON.stringify({ message: "userId is required" }),
      };
    }
    const user = await this.userService.getUserById(body.userId);
    if (!user) {
      return {
        statusCode: 404,
        body: JSON.stringify({ message: "User not found" }),
      };
    }
    return {
      statusCode: 200,
      body: JSON.stringify(user),
    };
  }

  async getUserStyleProfile(event: APIGatewayProxyEvent, context: Context): Promise<APIGatewayProxyResult> {
    const body = JSON.parse(event.body || "{}");
    if (!body.userId) {
      return {
        statusCode: 400,
        body: JSON.stringify({ message: "userId is required" }),
      };
    }
    try {
      const styleProfile = await this.userService.getUserStyleProfile(body.userId);
      if (!styleProfile) {
        return {
          statusCode: 404,
          body: JSON.stringify({ message: "Style profile not found" }),
        };
      }
      return {
        statusCode: 200,
        body: JSON.stringify(styleProfile),
      };
    } catch (error) {
      console.error("Error in getUserStyleProfile:", error);
      return {
        statusCode: 500,
        body: JSON.stringify({ message: "Internal server error" }),
      };
    }
  }
} 