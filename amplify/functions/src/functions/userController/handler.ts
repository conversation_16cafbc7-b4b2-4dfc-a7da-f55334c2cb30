import { APIGatewayProxy<PERSON><PERSON><PERSON>, APIGatewayProxyResult } from "aws-lambda";
import { Amplify } from "aws-amplify";
import outputs from "../../../../../amplify_outputs.json";
import { router } from "./routes";

Amplify.configure(outputs);

console.log(`[USER] Initialized Controller`);

export const handler: APIGatewayProxyHandler = async (
  event,
  context
): Promise<APIGatewayProxyResult> => {
  console.log("Request received:", {
    path: event.path,
    method: event.httpMethod,
    body: event.body,
    requestId: context.awsRequestId,
    timestamp: new Date().toISOString(),
  });

  try {
    return await router(event, context);
  } catch (error) {
    console.error("Error in handler:", error);
    return {
      statusCode: 500,
      body: JSON.stringify({ message: "Internal server error" }),
    };
  }
};
