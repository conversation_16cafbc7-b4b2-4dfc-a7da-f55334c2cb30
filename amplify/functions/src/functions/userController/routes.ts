import { APIGatewayProxyEvent, APIGatewayProxyResult, Context } from "aws-lambda";
import { UserController } from "./controllers/userController";

const userController = new UserController();

export const router = async (
  event: APIGatewayProxyEvent,
  context: Context
): Promise<APIGatewayProxyResult> => {
  const method = event.httpMethod.toUpperCase();
  let body: any = {};
  try {
    body = JSON.parse(event.body || "{}");
  } catch (e) {}

  if (method === "POST") {
    switch (body.operation) {
      case "create":
        return await userController.createUser(event, context);
      case "update":
        return await userController.updateUser(event, context);
      case "get":
        return await userController.getUser(event, context);
      case "getUserStyleProfile":
        return await userController.getUserStyleProfile(event, context);
      default:
        return {
          statusCode: 400,
          body: JSON.stringify({ message: "Invalid operation" })
        };
    }
  }

  return {
    statusCode: 405,
    body: JSON.stringify({ message: "Method not allowed" })
  };
}; 