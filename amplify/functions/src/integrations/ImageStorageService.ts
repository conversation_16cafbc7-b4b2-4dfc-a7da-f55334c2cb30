import { Amplify } from "aws-amplify";
import outputs from "../../../../amplify_outputs.json";
Amplify.configure(outputs);

import crypto from "crypto";

import {
  BlobServiceClient,
  BlockBlobClient,
  BlobSASPermissions,
} from "@azure/storage-blob";

export class ImageStorageService {
  private connectionString: string;
  private blobServiceClient: BlobServiceClient;
  private containerClient;
  private readonly algorithm = "aes-256-gcm";
  private readonly keyLength = 32;
  private readonly ivLength = 12;
  private readonly encryptionKey: Buffer;

  constructor() {
    this.connectionString =
      "DefaultEndpointsProtocol=https;AccountName=monovaoutfits;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net";
    this.blobServiceClient = BlobServiceClient.fromConnectionString(
      this.connectionString
    );
    this.containerClient =
      this.blobServiceClient.getContainerClient("ai-outfits");
    // Initialize encryption key
    const secretKey =
      process.env.URL_ENCRYPTION_KEY || "monova-image-encryption-key-32-chars!";
    this.encryptionKey = crypto.scryptSync(
      secretKey,
      "monova-salt",
      this.keyLength
    );
  }

  async uploadImage(imageUrl: string , caseId: string): Promise<string> {
    try {
      console.log(`[Image Storage Service] Processing image: ${imageUrl}`);

      const response = await fetch(imageUrl);
      const blob = await response.blob();
      const arrayBuffer = await blob.arrayBuffer();
      const buffer = Buffer.from(arrayBuffer);

      const blobName = `${caseId}-${Math.random()
        .toString(36)
        .substring(7)}.jpg`;
      const blockBlobClient: BlockBlobClient =
        this.containerClient.getBlockBlobClient(blobName);

      await blockBlobClient.uploadData(buffer);
      console.log("[Image Storage Service] Image uploaded successfully");

      return blockBlobClient.url;
    } catch (error) {
      throw new Error(`Failed to upload image: ${error}`);
    }
  }

  //---------------------------------<INCASE THE WE REMOVE PUBLIC ACCESS TO IMAGES IN FUTURE>---------------------------------//
  async generateSasUrl(blobUrl: string): Promise<string> {
    try {
      const blobName = new URL(blobUrl).pathname.split("/").pop();
      if (blobName === undefined) throw Error("Blob name not found in URL");
      const blockBlobClient: BlockBlobClient =
        this.containerClient.getBlockBlobClient(blobName);

      const expiresAt = new Date();
      expiresAt.setHours(expiresAt.getHours() + 1);

      const sasUrl = await blockBlobClient.generateSasUrl({
        permissions: BlobSASPermissions.parse("r"),
        expiresOn: expiresAt,
      });

      console.log("[Image Storage Service] SAS URL generated successfully");
      return sasUrl;
    } catch (error) {
      throw new Error(`Failed to generate SAS URL: ${error}`);
    }
  }

  /**
   * === URL Encryption/Decryption Service ===
   *
   * ! Security Implementation:
   * - Algorithm: AES-256-GCM (Advanced Encryption Standard with 256-bit key in Galois/Counter Mode)
   * - Key Length: 32 bytes (256 bits)
   * - IV Length: 12 bytes (96 bits)
   * - Auth Tag Length: 16 bytes (128 bits)
   *
   * ! Key Components:
   * 1. Encryption Key: Derived using scrypt (password-based key derivation)
   * 2. IV (Initialization Vector): Random value for each encryption
   * 3. Auth Tag: Ensures data integrity and authenticity
   *
   * * The encrypted output format is: base64(iv:encryptedData:authTag)
   */

  /**
   * Encrypts a URL into a secure, transmissible format
   *
   *Process Flow:
   * 1. Generate random IV (Initialization Vector)
   *    - ! Each encryption uses a unique IV for security
   *    - * Prevents identical URLs from producing identical encrypted output
   *
   * 2. Create Cipher
   *    - Uses AES-256-GCM algorithm
   *    - Combines encryption key and IV
   *
   * 3. Encrypt URL
   *    - Converts URL to buffer
   *    - Processes through cipher
   *    - ! GCM mode provides authentication along with encryption
   *
   * 4. Get Authentication Tag
   *    - * Generated by GCM mode
   *    - ! Ensures data integrity and authenticity
   *
   * 5. Combine Components
   *    - Format: iv:encryptedData:authTag
   *    - * Components are hex encoded individually
   *    - ! Final output is base64 encoded for safe transmission
   *
   * Returns: Base64 encoded string containing all components
   */

  getEncodedImageUrl(url: string): string {
    try {
      // Generate random IV
      const iv = crypto.randomBytes(this.ivLength);

      // Create cipher
      const cipher = crypto.createCipheriv(
        this.algorithm,
        this.encryptionKey,
        iv
      );

      // Encrypt URL
      const encryptedData = Buffer.concat([
        cipher.update(url, "utf8"),
        cipher.final(),
      ]);

      // Get auth tag
      const authTag = cipher.getAuthTag();

      // Convert all components to base64 individually and join with colons
      // ! This format matches Flutter's implementation
      const encodedUrl = [
        iv.toString("base64"),
        encryptedData.toString("base64"),
        authTag.toString("base64"),
      ].join(":");

      return encodedUrl;
    } catch (error) {
      throw new Error(`Failed to encode URL: ${error}`);
    }
  }

  /**
   * Decrypts an encoded URL back to its original form
   *
   * Process Flow:
   * 1. Decode Base64
   *    - Converts transmitted string back to original format
   *    - * Splits into original components (iv:encrypted:authTag)
   *
   * 2. Extract Components
   *    - Convert hex strings back to buffers
   *    - ! Each component must be properly reconstructed
   *
   * 3. Create Decipher
   *    - Uses same AES-256-GCM algorithm
   *    - Requires original IV for correct decryption
   *
   * 4. Set Auth Tag
   *    - ! Must be set before decryption begins
   *    - * Verification happens during decryption
   *
   * 5. Decrypt Data
   *    - Processes encrypted data through decipher
   *    - ! Will fail if data has been tampered with
   *    - * Concatenates all decrypted chunks
   *
   * 6. Convert to String
   *    - Transforms decrypted buffer back to URL string
   *
   * ! Error Cases:
   * - Invalid base64 encoding
   * - Corrupted/modified data
   * - Missing components
   * - Authentication tag mismatch
   *
   * Returns: Original URL string if decryption successful
   */

  getDecodedImageUrl(encodedUrl: string): string {
    try {
      // Split the components
      const [ivBase64, encryptedBase64, authTagBase64] = encodedUrl.split(":");

      if (!ivBase64 || !encryptedBase64 || !authTagBase64) {
        throw new Error("Invalid encoded URL format");
      }

      // Convert from base64 to buffers
      const iv = Buffer.from(ivBase64, "base64");
      const encrypted = Buffer.from(encryptedBase64, "base64");
      const authTag = Buffer.from(authTagBase64, "base64");

      // Create decipher
      const decipher = crypto.createDecipheriv(
        this.algorithm,
        this.encryptionKey,
        iv
      );
      decipher.setAuthTag(authTag);

      // Decrypt
      const decrypted = Buffer.concat([
        decipher.update(encrypted),
        decipher.final(),
      ]);

      console.log("[Image Storage Service] URL decoded successfully");
      return decrypted.toString("utf8");
    } catch (error) {
      console.error("[Image Storage Service] Error decoding URL:", error);
      throw new Error("Failed to decode URL");
    }
  }
}
