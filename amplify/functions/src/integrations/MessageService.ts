import axios from "axios";
import { MessagePlatform } from "../config/constants";
import { ChatService } from "../services/chatService";

/**
 * Service class for handling WhatsApp Cloud API message operations
 */
export class MessageService {
  private WhatsappVersion: string;
  private WhatsappBusinessId: string;
  private WhatsappAccessToken: string;
  private WhatsappBaseUrl: string;
  private WhatsappMediaBaseUrl: string;
  private chatService: ChatService;

  constructor() {
    (this.WhatsappVersion = "v21.0"),
      (this.WhatsappBusinessId =
        (process.env.WHATSAPP_BUSINESS_ID as string) || "440678912463914");
    this.WhatsappAccessToken =
      (process.env.WHATSAPP_TOKEN as string) ||
      "EAA1y2Bhk2S0BO3bHzTwTqHM17qm3RfbkkwNMtM2lTske92I24dGaaLUGRc9yTJezjUYoNWbByzSXaKVUUiMQmZAS6RhJ7rAAi3jRw3ptOMNMZAw4QQEexZCxufu01GZBjeZBGksmxVj3gBERZAEbuHowWCagIPqRfP167bdOemKLVFDaj9eJvuucayySinK1jLxwZDZD";
    this.WhatsappBaseUrl = `https://graph.facebook.com/${this.WhatsappVersion}/${this.WhatsappBusinessId}/messages`;
    this.WhatsappMediaBaseUrl = `https://graph.facebook.com/${this.WhatsappVersion}`;
    this.chatService = new ChatService();
  }

  async processWhatsappImageMessage(mediaId: string) {
    console.log(
      `[Message Service][Whatsapp media url] Retrieving URL for media ID: ${mediaId}`
    );
    const response = await axios.get(
      `${this.WhatsappMediaBaseUrl}/${mediaId}`,
      {
        headers: {
          Authorization: `Bearer ${this.WhatsappAccessToken}`,
        },
        params: {
          phone_number_id: this.WhatsappBusinessId,
        },
      }
    );
    if (!response.data?.url) {
      throw new Error("Media URL not found in response");
    }

    console.log(
      `[Message Service][Whatsapp media url] Successfully retrieved URL for media ID: ${mediaId}`
    );

    return response.data.url;
  }
  /**
   * Makes a request to the WhatsApp Cloud API
   */

  async makeRequestToWhatsapp(payload: any) {
    console.log("=== Starting WhatsApp API Request ===", {
      url: this.WhatsappBaseUrl,
      timestamp: new Date().toISOString(),
    });

    try {
      console.log("[MESSAGE_SERVICE][WHATSAPP]Request payload:", {
        to: payload.to,
        type: payload.type,
        hasContext: !!payload.context,
        payloadSize: JSON.stringify(payload).length,
      });

      const response = await axios({
        method: "POST",
        url: this.WhatsappBaseUrl,
        data: {
          messaging_product: "whatsapp",
          recipient_type: "individual",
          ...payload,
        },
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${this.WhatsappAccessToken}`,
        },
      });

      console.log("[MESSAGE_SERVICE][WHATSAPP] WhatsApp API Response:", {
        status: response.status,
        statusText: response.statusText,
        messageId: response.data?.messages?.[0]?.id,
        recipientId: response.data?.contacts?.[0]?.wa_id,
      });

      return response.data;
    } catch (error: any) {
      console.error(
        "[MESSAGE_SERVICVE][WHATSAPP]=== WhatsApp API Request Failed ===",
        {
          error,
          errorType:
            error instanceof Error ? error.constructor.name : "Unknown",
          errorMessage:
            error instanceof Error ? error.message : "Unknown error",
          errorStack: error instanceof Error ? error.stack : undefined,
          timestamp: new Date().toISOString(),
        }
      );

      if (error.response) {
        console.error(
          "[MESSAGE_SERVICE][WHATSAPP] WhatsApp API Error Details:",
          {
            status: error.response.status,
            statusText: error.response.statusText,
            errorCode: error.response.data?.error?.code,
            errorSubcode: error.response.data?.error?.subcode,
            errorMessage: error.response.data?.error?.message,
            fbtraceId: error.response.headers?.["x-fb-trace-id"],
          }
        );
      }

      throw error;
    }
  }

  async makeRequestToChatapp(payload: any): Promise<any> {
    // Use ChatService for chat app messages
    return this.chatService.sendMessage(payload);
  }

  async makeRequest(payload: any, messagePlatform: MessagePlatform): Promise<any> {
    try {
      if (messagePlatform === MessagePlatform.WHATSAPP)
        return this.makeRequestToWhatsapp(payload);
      if (messagePlatform === MessagePlatform.NATIVE)
        return this.makeRequestToChatapp(payload);
      return null;
    } catch (error) {
      console.error("[MESSAGE_SERVICE][CHATAPP] === Request Failed ===", {
        error,
        errorType: error instanceof Error ? error.constructor.name : "Unknown",
        errorMessage: error instanceof Error ? error.message : "Unknown error",
        errorStack: error instanceof Error ? error.stack : undefined,
        timestamp: new Date().toISOString(),
      });
      throw error;
    }
  }

  /**
   * Sends a text message
   */
  async sendTextMessage(
    to: string,
    text: string,
    options: any = {},
    messagePlatform: MessagePlatform
  ): Promise<any> {
    console.log("[MESSAGE_SERVICE]=== Preparing Text Message ===", {
      recipientNumber: to,
      textLength: text.length,
      hasOptions: Object.keys(options).length > 0,
    });

    try {
      const payload = {
        to,
        type: "text",
        text: {
          preview_url: false,
          body: text,
        },
        ...(options.messageId && {
          context: { message_id: options.messageId },
        }),
      };

      console.log("[MESSAGE_SERVICE]Text message payload prepared:", {
        recipientNumber: to,
        messageType: "text",
        hasContext: !!options.messageId,
        previewUrl: payload.text.preview_url,
      });

      const response = await this.makeRequest(payload, messagePlatform);

      console.log("[MESSAGE_SERVICE]Text message sent successfully:", {
        messageId: response.messages?.[0]?.id,
        recipientId: response.contacts?.[0]?.wa_id,
        timestamp: new Date().toISOString(),
      });

      return response;
    } catch (error) {
      console.error("[MESSAGE_SERVICE]=== Error Sending Text Message ===", {
        error,
        recipientNumber: to,
        textLength: text.length,
        options,
        errorMessage: error instanceof Error ? error.message : "Unknown error",
        timestamp: new Date().toISOString(),
      });
      throw error;
    }
  }

  /**
   * Sends an image message
   */
  async sendImageMessage(
    to: string,
    image: any,
    options: any = {},
    messagePlatform: MessagePlatform
  ) {
    if (messagePlatform === MessagePlatform.NATIVE && image.url) {
      console.log(`[MESSAGE_SERVICE]=== Encoding Image URL ===`);
      //! NOTE : WILL BE ADDED ONCE IMAGE encoding is cross platform campatible
      // image.url = this.ImageStorageService.getEncodedImageUrl(image.url);
      image.url = image.url;
    }
    const payload = {
      to,
      type: "image",
      image: {
        ...(image.url ? { link: image.url } : { id: image.id }),
        ...(options.caption && { caption: options.caption }),
      },
      ...(options.messageId && {
        context: { message_id: options.messageId },
      }),
    };
    return this.makeRequest(payload, messagePlatform);
  }

  /**
   * Sends a template message
   */
  async sendTemplateMessage(
    to: string,
    templateName: string,
    languageCode: string,
    components: any[] = [],
    messagePlatform: MessagePlatform
  ) {
    const payload = {
      to,
      type: "template",
      template: {
        name: templateName,
        language: {
          code: languageCode,
        },
        components,
      },
    };
    return this.makeRequest(payload, messagePlatform);
  }

  /**
   * Sends an interactive message (buttons or list)
   */
  async sendInteractiveMessage(
    to: string,
    interactive: {
      type: "button" | "list";
      header?: {
        type: string;
        text?: string;
        image?: { link: string };
        video?: { link: string };
        document?: { link: string };
      };
      body: { text: string };
      footer?: { text: string };
      action: {
        buttons?: Array<{ id: string; title: string }>;
        button?: string;
        sections?: Array<{
          title?: string;
          rows: Array<{ id: string; title: string; description?: string }>;
        }>;
      };
    },
    options: { messageId?: string } = {},
    messagePlatform: MessagePlatform
  ) {
    console.log("[MESSAGE_SERVICE] === Preparing Interactive Message ===", {
      recipientNumber: to,
      interactiveType: interactive.type,
      hasHeader: !!interactive.header,
      hasFooter: !!interactive.footer,
      hasButtons: !!interactive.action.buttons,
      hasSections: !!interactive.action.sections,
      hasContext: !!options.messageId,
    });

    console.log(
      "[MESSAGE_SERVICE] === Interactive Message Full Payload ===\n",
      JSON.stringify(interactive, null, 2)
    );

    try {
      const payload = {
        to,
        type: "interactive",
        interactive: {
          type: interactive.type,
          ...(interactive.header && { header: interactive.header }),
          body: interactive.body,
          ...(interactive.footer && { footer: interactive.footer }),
          action: interactive.action,
        },
        ...(options.messageId && {
          context: { message_id: options.messageId },
        }),
      };

      console.log("[MESSAGE_SERVICE] Interactive message payload prepared:", {
        recipientNumber: to,
        messageType: "interactive",
        interactiveType: interactive.type,
        bodyLength: interactive?.body?.text?.length,
        buttonCount: interactive.action.buttons?.length,
        sectionCount: interactive.action.sections?.length,
      });

      console.log(
        `Payload being sent to ${messagePlatform} interactive message -> ${JSON.stringify(
          payload
        )}`
      );

      const response = await this.makeRequest(payload, messagePlatform);

      console.log("[MESSAGE_SERVICE] Interactive message sent successfully:", {
        messageId: response.messages?.[0]?.id,
        recipientId: response.contacts?.[0]?.wa_id,
        timestamp: new Date().toISOString(),
      });

      return response;
    } catch (error) {
      console.error(
        "[MESSAGE_SERVICE] === Error Sending Interactive Message ===",
        {
          error,
          recipientNumber: to,
          interactiveType: interactive.type,
          options,
          errorType:
            error instanceof Error ? error.constructor.name : "Unknown",
          errorMessage:
            error instanceof Error ? error.message : "Unknown error",
          timestamp: new Date().toISOString(),
        }
      );
      throw error;
    }
  }

  // Add more message type methods as needed...
}
