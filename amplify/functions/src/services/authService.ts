import crypto from "crypto";
import { AuthDao } from "../db/authDao";
import { UserInfoDao } from "../db/userInfoDao";
import jwt, { SignOptions, Secret } from "jsonwebtoken";
import { UserRole } from "../config/constants";

const DEFAULT_ADMIN_PREFIX = "1"; // Prefix for admin numbers

export class AuthService {
  private authDao: AuthDao;
  private userInfoDao: UserInfoDao;
  private readonly JWT_SECRET: Secret;
  private readonly TOKEN_EXPIRY: SignOptions["expiresIn"];
  private readonly adminIds: string[];

  constructor() {
    this.authDao = new AuthDao();
    this.userInfoDao = new UserInfoDao();
    this.JWT_SECRET = process.env.JWT_SECRET || "SECRET_KEY";
    //! MUST KEEP IT AS A NUMBER AND NOT A STRING
    this.TOKEN_EXPIRY = Number(process.env.TOKEN_EXPIRY) || 30 * 24 * 60 * 60; // 30 days in seconds

    // Initialize admin phone numbers from env or fallback to default
    const envAdminIds =
      process.env.ADMIN_PHONE_NUMBERS?.split(",").filter(Boolean);
    this.adminIds = envAdminIds?.length ? envAdminIds : [];

    console.log(
      "[AUTH][SERVICE] Initialized with admin numbers:",
      this.adminIds.map((num) => num.substring(0, 6) + "****")
    ); // Log masked numbers for security
  }

  generateRandomId(length: number = 32): string {
    return crypto
      .randomBytes(length)
      .toString("base64")
      .replace(/[^a-zA-Z0-9]/g, "")
      .slice(0, length);
  }

  private isAdmin(userId: string): boolean {
    console.log("[AUTH][SERVICE] : Checking is Admin", userId);
    const modifiedUserId = userId.substring(2);
    console.log("[AUTH][SERVICE] : Modified User Id", modifiedUserId);
    console.log(
      "[AUTH][SERVICE] : Starts With : ",
      modifiedUserId.startsWith(DEFAULT_ADMIN_PREFIX)
    );
    return modifiedUserId.startsWith(DEFAULT_ADMIN_PREFIX);
  }

  private generateOTP(userId: string): string {
    console.log(`[AUTH][SERVICE] Generating OTP`);

    // Check if user is admin
    if (this.isAdmin(userId)) {
      console.log(
        `[AUTH][SERVICE] Admin user detected, returning fixed OTP`,
        userId.substring(2, 8)
      );
      return userId.substring(2, 8); // Use the first 5 digits of the userId as OTP
    }

    const buffer = crypto.randomBytes(3);
    const num = buffer.readUIntBE(0, 3);
    const otp = String(num % 1000000).padStart(6, "0");
    console.log(`[AUTH][SERVICE] Generated OTP: ${otp}`);
    return otp;
  }

  private calculateExpiryTime(): number {
    console.log(`[AUTH][SERVICE] Calculating expiry time`);
    //! NOTE : EXPIRY SHOULD BE A PART ENV VARIABLES
    return Math.floor(Date.now() / 1000) + 5 * 60; // 5 minutes from now
  }

  // NOTE : BOTH  COULD BE A PART OF A SINGLE FUNCTION
  async generateSignupOTP(userId: string) {
    const existingUser = await this.userInfoDao.getUserInfoById(userId);
    if (existingUser?.data) {
      throw new Error("User already exists");
    }

    const otp = this.generateOTP(userId);
    const expiresAt = this.calculateExpiryTime();

    console.log(`[AUTH][SERVICE] Deleting OTP record for userId: ${userId}`);
    await this.authDao.deleteOTPRecord(userId);
    console.log(`[AUTH][SERVICE] Creating OTP record for userId: ${userId}`);
    await this.authDao.createOTPRecord(userId, otp, expiresAt);

    return {
      userId,
      expiresAt,
    };
  }

  async generateLoginOTP(userId: string) {
    const existingUser = await this.userInfoDao.getUserInfoById(userId);
    if (!existingUser?.data) {
      throw new Error("User not found");
    }
    const otp = this.generateOTP(userId);
    const expiresAt = this.calculateExpiryTime();

    console.log(`[AUTH][SERVICE] Deleting OTP record for userId: ${userId}`);
    await this.authDao.deleteOTPRecord(userId);

    console.log(`[AUTH][SERVICE] Creating OTP record for userId: ${userId}`);
    await this.authDao.createOTPRecord(userId, otp, expiresAt);

    return {
      userId,
      expiresAt,
    };
  }

  async generateAuthToken(payload: {
    userId: string;
    userFullName: string;
    userWhatsAppNumber: string;
    userRole: UserRole;
  }) {
    if (!payload.userId || !payload.userRole) {
      throw new Error("Invalid token payload");
    }

    console.log(
      `[AUTH][SERVICE] Generating JWT token for user: ${payload.userId}`
    );
    const token = jwt.sign(payload, this.JWT_SECRET, {
      expiresIn: this.TOKEN_EXPIRY,
    });
    console.log(
      `[AUTH][SERVICE] Generated JWT token for user: ${payload.userId}`
    );
    return token;
  }

  async verifyOTP(userId: string, inputOTP: string, userFullName: string) {
    try {
      // Validate inputs
      if (!userId || !inputOTP) {
        throw new Error("Missing required parameters");
      }

      const record = await this.authDao.getOTPRecord(userId);
      if (!record?.data) {
        throw new Error("No OTP found");
      }

      const { otp, expiresAt } = record.data;
      const currentTime = Math.floor(Date.now() / 1000);

      if (currentTime > expiresAt) {
        await this.authDao.deleteOTPRecord(userId);
        throw new Error("OTP expired");
      }

      if (otp !== inputOTP) {
        throw new Error("Invalid OTP");
      }

      console.log(
        `[AUTH][SERVICE] Checking if User Exists with userId: ${userId}`
      );
      const existingUser = await this.userInfoDao.getUserInfoById(userId);
      console.log(`[AUTH][SERVICE] Existing User Data`, existingUser);

      // Determine user role
      let userRole: UserRole = this.adminIds.includes(userId)
        ? UserRole.ADMIN
        : UserRole.USER;

      if (!existingUser?.data?.userFullName && !userFullName) {
        throw new Error("User Full Name is not provided");
      }

      // Check for existing token
      const existingToken = existingUser?.data?.token;
      const isTokenValid =
        existingToken && (await this.verifyAuthToken(existingToken)).success;

      let token;
      if (isTokenValid) {
        console.log(
          `[AUTH][SERVICE] Using existing valid token for user: ${userId}`
        );
        token = existingToken; // Use the existing token
      } else {
        // Generate token payload
        const tokenPayload = {
          userId,
          userFullName: existingUser?.data?.userFullName || userFullName,
          userWhatsAppNumber: userId,
          userRole,
        };

        console.log(`[AUTH][SERVICE] Generating JWT token for user: ${userId}`);
        token = await this.generateAuthToken(tokenPayload);
      }

      if (existingUser?.data) {
        console.log(`[AUTH][SERVICE] Updating existing user: ${userId}`);
        await this.userInfoDao.updateUserInfo(userId, {
          token,
          userRole,
          userFullName: existingUser?.data?.userFullName || userFullName, // Ensure name is updated if changed
        });
      } else {
        console.log(`[AUTH][SERVICE] Creating new user: ${userId}`, {
          userId,
          fullname: userFullName,
          token,
          userRole,
        });

        await this.userInfoDao.createUserInfo(
          userId,
          userFullName,
          userId,
          "NEW",
          userRole,
          token
        );
      }

      // Clean up OTP record after successful verification
      await this.authDao.deleteOTPRecord(userId);

      return {
        authToken: token,
        userId,
        userFullName: userFullName,
        userRole,
      };
    } catch (error) {
      console.error(
        `[AUTH][SERVICE] Error in token generation/user management:`,
        error instanceof Error
          ? {
              message: error.message,
              stack: error.stack,
            }
          : error
      );
      throw error;
    }
  }

  async verifyAuthToken(token: string) {
    console.log(`[AUTH][SERVICE] Verifying auth token`);

    try {
      // Verify JWT and decode payload
      console.log(`[AUTH][SERVICE] Decoding JWT token`);
      const decoded = jwt.verify(token, this.JWT_SECRET) as {
        userId: string;
        userFullName: string;
        userWhatsAppNumber: string;
        userRole?: UserRole;
      };

      // Get user from database
      console.log(
        `[AUTH][SERVICE] Fetching user info for userId: ${decoded.userId}`
      );
      const user = await this.userInfoDao.getUserInfoById(decoded.userId);

      console.log(`[AUTH][SERVICE] User data`, user);
      if (!user?.data) {
        throw new Error("User not found");
      }

      console.log(`[AUTH][SERVICE] Checking if token matches stored token`);
      // Check if token matches stored token
      if (user.data?.token !== token) {
        throw new Error("Invalid token");
      }

      // If there is no user Role inside user Record which is only possible for old users when role field was not present
      // We will make role required in user info schema
      //! then we can either warn/ throw error or switch role to guest
      if (!user.data?.userRole) {
        // For now let's make the client a guest
        console.warn(
          `[AUTH SERViCE][VERY AUTH TOKEN] No User Role Found`,
          user.data
        );
        console.log(`[AUTH SERVICE] Checking if user is ADMIN`);
        // Check if user's phone number is in admin list and update role accordingly
        const isAdmin = this.adminIds.includes(user.data.userId);
        const updatedRole = isAdmin ? UserRole.ADMIN : UserRole.GUEST;

        console.log(`[AUTH SERVICE] User role updated to ${updatedRole}`, {
          userWhatsAppNumber: user.data.userWhatsAppNumber,
          isAdmin,
        });

        // Update user role in database
        await this.userInfoDao.updateUserInfo(decoded.userId, {
          userRole: updatedRole,
        });
      }

      console.log(`[AUTH][SERVICE] Token verified successfully`);
      return {
        success: true,
        user: {
          userId: decoded.userId,
          userFullName: decoded.userFullName,
          userWhatsAppNumber: decoded.userWhatsAppNumber,
          userRole: decoded.userRole,
        },
      };
    } catch (error) {
      console.error("[AUTH][SERVICE] Token verification failed:", error);

      if (error instanceof jwt.TokenExpiredError) {
        throw new Error("Token expired");
      }

      if (error instanceof jwt.JsonWebTokenError) {
        throw new Error("Invalid token");
      }

      throw error;
    }
  }

  async createGuestUser() {
    const userId = `GUEST_ ${this.generateRandomId()}`;
    const userFullName = "Guest User";
    const userWhatsAppNumber = userId;
    const userRole = UserRole.GUEST;
    const token = await this.generateAuthToken({
      userId,
      userFullName,
      userWhatsAppNumber,
      userRole,
    });

    await this.userInfoDao.createUserInfo(
      userId,
      userFullName,
      userWhatsAppNumber,
      "NEW",
      userRole,
      token
    );

    return {
      userId,
      userFullName,
      token,
      userRole,
    };
  }
}
