import { Case<PERSON>ao } from "../db/caseDao";
import { MessageDao } from "../db/messageDao";
import { CaseStatus } from "../config/constants";
import { v4 as uuidv4 } from "uuid";
import type {
  CreateCaseInput,
  UpdateCaseInput,
  ListCasesInput,
} from "../types";
import { AIService } from "../ai/AzureAIIntegrationService";
import { MessageFormatter } from "../utils/MessageFormatter";

export class CaseService {
  private caseDao: CaseDao;
  private messageDao: MessageDao;
  private aiService: AIService;

  constructor(aiService: AIService) {
    console.log("Initializing CaseService...");
    this.caseDao = new CaseDao();
    this.messageDao = new MessageDao();
    this.aiService = aiService;
    console.log("CaseService initialized with CaseDao and MessageDao");
  }

  async createCase(input: CreateCaseInput) {
    console.log("Creating case with input:", input);
    try {
      const caseId = `${Number.MAX_SAFE_INTEGER - Date.now()}-${uuidv4()}`;
      const caseStartTime = new Date().toISOString();
      console.log("Generated case details:", { caseId, caseStartTime });

      const result = await this.caseDao.createCase(
        input.userId,
        caseId,
        caseStartTime,
        input.caseStatus || CaseStatus.NEW,
        input.messages || []
      );
      console.log("Case created successfully:", result);
      return result;
    } catch (error) {
      console.error("Error in createCase:", { error, input });
      throw error;
    }
  }

  async updateCase(userId: string, caseId: string, updates: UpdateCaseInput) {
    console.log("Updating case:", { userId, caseId, updates });
    try {
      const existingCase = await this.caseDao.getCaseById(userId, caseId);
      console.log("Existing case found:", existingCase);

      if (!existingCase) {
        console.error("Case not found:", { userId, caseId });
        throw new Error("Case not found");
      }

      try {
        const updateData = { ...updates };

        if (updates.caseStatus === CaseStatus.CLOSED) {
          updateData.caseEndTime = new Date().toISOString();
        }

        console.log("Attempting update with original caseContext:", updateData);
        const result = await this.caseDao.updateCase(
          userId,
          caseId,
          updateData
        );
        console.log("Case updated successfully with original context:", result);
        return result;
      } catch (error) {
        console.warn("Failed to update with original caseContext:", error);

        // Second try: Update without caseContext
        delete updates.caseContext;

        if (updates.caseStatus === CaseStatus.CLOSED) {
          updates.caseEndTime = new Date().toISOString();
        }

        console.log("Attempting update without caseContext:", updates);
        const fallbackResult = await this.caseDao.updateCase(
          userId,
          caseId,
          updates
        );
        console.log(
          "Case updated successfully without context:",
          fallbackResult
        );
        return fallbackResult;
      }
    } catch (error) {
      console.error("Error in updateCase:", { error, userId, caseId, updates });
      throw error;
    }
  }

  async closeCase(userId: string, caseId: string) {
    try {
      console.log("Closing case:", { userId, caseId });
      const existingCase = await this.getCaseById(userId, caseId);
      if (!existingCase) {
        console.error("Case not found:", { userId, caseId });
        throw new Error("Case not found");
      }
      console.log("[CASE SERVICE]Case data:", existingCase);
      let useCase = existingCase.data?.useCase;
      console.log(`[CASE SERVICE] Use case: ${useCase}`);
      if (!useCase) {
        console.warn("Use case not found for caseId:", caseId);
        console.log(`[CASE SERVICE] Creating title without useCae`);
        useCase = "Unknown";
      }
      //FETCH MESSAGES
      const messages = (await this.getMessagesByCaseId(caseId)) || [];
      console.log("[CASE SERVICE]Messages:", messages);
      const caseTitle = await this.generateCaseTitle(
        userId,
        caseId,
        messages,
        useCase
      );
      console.log("[CASE SERVICE]Case title:", caseTitle);

      const result = await this.caseDao.updateCase(userId, caseId, {
        caseStatus: CaseStatus.CLOSED,
        caseTitle: caseTitle,
        caseEndTime: new Date().toISOString(),
      });

      console.log("Case closed successfully:", result);
      return result;
    } catch (error) {
      console.error("Error in closeCase:", { error, userId, caseId });
      throw error;
    }
  }

  async generateCaseTitle(
    userId: string,
    caseId: string,
    messages: any[],
    useCase: string
  ) {
    try {
      console.log(`[CaseService][handleCaseUpdate] Updating case:`, {
        caseId,
        userId,
      });

      let formattedMessages = MessageFormatter.formatMessages(messages);
      console.log(`[CaseService][handleCaseUpdate] Formatted messages:`, {
        formattedMessages,
      });

      // Generate case title if status is CLOSED
      let caseTitle;
      const titlePrompt = `You are a helpful AI assistant. Based on the conversation provided, generate a short, concise title (maximum 5-6 words) that summarizes the main topic or purpose of this conversation. The title should be clear and descriptive, helping users quickly understand what the conversation was about. Do not use any special characters or emojis.
  
  Format the output as just the title text, without quotes or additional formatting.
  Use the "useCase" paramerter provided in input params to curate the title
  Example good titles for each use case:
  For AMA:
  - Fashion Advice for Petite Body Types
  - Styling Tips for Business Casual
  - Color Matching Guidelines Discussion
  
  For Review Outfit:
  - Date Night Outfit Feedback
  - Interview Look Assessment
  - Wedding Guest Attire Review
  
  For Curate Outfit:
  - Casual Weekend Outfit Creation
  - Summer Beach Party Look
  - Professional Work Attire Styling`;

      console.log(`[CaseService][handleCaseUpdate] Generating case title`);

      const inputPayload = {
        useCase,
        formattedMessages,
      };

      caseTitle = await this.aiService.getAIResponse(
        [JSON.stringify(inputPayload)],
        titlePrompt,
        "gpt-4o-mini-2"
      );

      console.log(
        `[CaseService][handleCaseUpdate] Generated case title`,
        caseTitle
      );

      console.log(
        `[CaseService][handleCaseUpdate] Case updated successfully`,
        {
          caseId,
        }
      );
      return caseTitle;
    } catch (error) {
      console.error(`[CaseService][handleCaseUpdate] Error:`, {
        error,
        caseId,
        userId,
        errorMessage: error instanceof Error ? error.message : "Unknown error",
      });
      throw error;
    }
  }

  async addMessageToCase(userId: string, caseId: string, messageId: string) {
    console.log("Adding message to case:", { userId, caseId, messageId });
    try {
      const existingCase = await this.caseDao.getCaseById(userId, caseId);
      console.log("Existing case found:", existingCase);

      if (!existingCase) {
        console.error("Case not found:", { userId, caseId });
        throw new Error("Case not found");
      }

      const updatedMessages = [
        ...((existingCase.data?.messages?.filter(
          (msg: unknown): msg is string => msg != null
        ) || []) as string[]),
        messageId,
      ];
      console.log("Updated messages array:", updatedMessages);

      const result = await this.caseDao.updateCase(userId, caseId, {
        messages: updatedMessages,
      });
      console.log("Message added successfully:", result);
      return result;
    } catch (error) {
      console.error("Error in addMessageToCase:", {
        error,
        userId,
        caseId,
        messageId,
      });
      throw error;
    }
  }

  async getMessagesByCaseId(caseId: string) {
    console.log("Getting messages by case ID:", { caseId });
    try {
      const result = await this.messageDao.listMessagesByCase(caseId);
      console.log("Messages retrieval result:", result);
      return result;
    } catch (error) {
      console.error("Error in getMessagesByCaseId:", { error, caseId });
      throw error;
    }
  }

  async getCaseById(userId: string, caseId: string) {
    console.log("Getting case by ID:", { userId, caseId });
    try {
      const result = await this.caseDao.getCaseById(userId, caseId);
      console.log("Case retrieval result:", result);
      return result;
    } catch (error) {
      console.error("Error in getCaseById:", { error, userId, caseId });
      throw error;
    }
  }

  async getActiveCase(userId: string) {
    console.log("Getting active case for user:", userId);
    try {
      const cases = (await this.caseDao.listCasesByUser(userId)).items;
      console.log("Retrieved cases:", cases);

      const sortedCases = cases?.sort(
        (a, b) =>
          new Date(b.caseStartTime).getTime() - 
          new Date(a.caseStartTime).getTime()
      );
      console.log("Sorted cases:", sortedCases);

      const latestCase = sortedCases?.[0];
      const activeCase =
        latestCase && latestCase.caseStatus !== CaseStatus.CLOSED
          ? latestCase
          : null;
      console.log("Active case found:", activeCase);

      return activeCase;
    } catch (error) {
      console.error("Error in getActiveCase:", { error, userId });
      throw error;
    }
  }

  async listCases(params: ListCasesInput) {
    console.log("Listing cases with params:", params);
    try {
      const { userId, status, limit, startKey } = params;
      if (!userId) {
        console.error("userId is required");
        throw new Error("userId is required");
      }

      let cases = await this.caseDao.listCasesByUser(userId, limit, startKey);
      console.log("Retrieved cases:", cases);

      if (status) {
        const filteredItems = cases.items?.filter(
          (caseItem: any) => caseItem.caseStatus === status
        );
        console.log("Filtered cases by status:", {
          status,
          count: filteredItems?.length,
        });
        return {
          items: filteredItems,
          lastEvaluatedKey: cases.lastEvaluatedKey,
        };
      }

      return cases;
    } catch (error) {
      console.error("Error in listCases:", { error, params });
      throw error;
    }
  }
}