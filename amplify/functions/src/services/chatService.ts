import { MessagePlatform, MessageType } from "../config/constants";
import { MessageDao } from "../db/messageDao";
import { MessageService } from "../integrations/MessageService";
import { WhatsAppPayload } from "../models/whatsappPayload";
import { v4 as uuidv4 } from "uuid";
import { CaseService } from "../services/caseService";

interface MessageStatusUpdate {
  caseId: string;
  messageId: string;
  messageStatus: "DELIVERED" | "READ";
}

export class ChatService {
  private messageDao: MessageDao = new MessageDao();
  private caseService: CaseService;
  private messageService: MessageService = new MessageService();

  constructor(caseService: CaseService) {
    this.caseService = caseService;
  }

  async updateMessageStatuses(
    webhookBody: any,
    statusUpdates: MessageStatusUpdate[]
  ): Promise<void> {
    console.log(
      `[ChatService][updateMessageStatuses] tid=${statusUpdates
        .map((update) => update.messageId)
        .join(",")} Processing status updates:`,
      {
        count: statusUpdates.length,
        statuses: statusUpdates.map((u) => u.messageStatus),
      }
    );

    if (
      webhookBody.object === "whatsapp_business_account" &&
      webhookBody.entry &&
      webhookBody.entry[0].changes &&
      webhookBody.entry[0].changes[0].value.statuses
    ) {
      await this.messageDao.updateMessageStatuses(statusUpdates);
      console.log(
        `[ChatService][updateMessageStatuses] tid=${statusUpdates
          .map((update) => update.messageId)
          .join(", ")} Successfully updated message statuses:`
      );
    }
  }

  async sendMessage(payload: any) {
    console.log("[ChatService][sendMessage] Starting message send:", {
      recipientNumber: payload.recipientNumber,
      messageType: payload.messageType,
      messagePlatform: payload.messagePlatform,
    });

    console.log(
      "[ChatService][sendMessage] Full payload:",
      JSON.stringify(payload, null, 2)
    );

    WhatsAppPayload.validate(payload);

    const response = await this.processOutboundMessage(
      this.messageService,
      payload
    );

    const messageId = response?.messages?.[0]?.id || `camid_${uuidv4()}`; // In case of message Source == chat app we need to create a messsage id

    console.log(
      `[ChatService][sendMessage] tid=${messageId} Message sent successfully:`,
      {
        recipientNumber: payload.recipientNumber,
        messageType: payload.messageType,
      }
    );

    // Get appropriate message content based on type
    const messageContent = (() => {
      const messageType = payload.messageType.toLowerCase();
      switch (messageType) {
        case MessageType.TEXT:
          return payload.content.text;
        case MessageType.IMAGE:
        case MessageType.AUDIO:
          return {
            url: payload.content?.url,
            caption: payload.content?.caption,
          };
        case MessageType.DOCUMENT:
          return {
            url: payload.content?.url,
            filename: payload.content?.filename,
          };
        case MessageType.INTERACTIVE_LIST:
        case MessageType.INTERACTIVE_REPLY:
          return payload.content;
        // case MessageType.INTERACTIVE_INPUT:
        //   return payload.content;
        default:
          return "";
      }
    })();

    // Save the outbound message
    try {
      console.log(
        `[ChatService][sendMessage] tid=${messageId} Saving message to database:`,
        {
          caseId: payload.caseId,
          messageType: payload.messageType,
          messagePlatform: payload.messagePlatform,
        }
      );

      // For interactive messages, store as just "INTERACTIVE"
      payload.messageType = payload.messageType
        .toUpperCase()
        .startsWith("INTERACTIVE")
        ? "INTERACTIVE"
        : payload.messageType;

      // Update image URL to be encoded
      console.log(`[CHAT SERVICE] Message type: ${payload.messageType}`);
      if (
        payload.messageType.toUpperCase() === MessageType.IMAGE &&
        messageContent?.url
      ) {
        console.log(`[CHAT SERVICE] Encoding image URL: ${messageContent.url}`);
        // const imageUrl = this.imageStorageService.getEncodedImageUrl(
        //   messageContent.url
        // );
        const imageUrl = messageContent.url;
        console.log(`[CHAT SERVICE] Encoded image URL: ${imageUrl}`);
        messageContent.url = imageUrl;
      }

      // In case of message Source == chat app we need to create a messsage id
      const savedMessage = await this.messageDao.createMessage(
        payload.caseId,
        messageId,
        payload.recipientNumber,
        "OUTBOUND",
        "SENT",
        payload.messageType.toUpperCase(),
        messageContent,
        payload.messagePlatform,
        payload.messageOrigin
      );

      console.log(
        `[ChatService][sendMessage] tid=${response.messages?.[0]?.id} Message saved successfully:`,
        {
          caseId: payload.caseId,
        },
        savedMessage
      );

      await this.caseService.addMessageToCase(payload.recipientNumber, payload.caseId, messageId);
    } catch (error) {
      console.error(
        `[ChatService][sendMessage] tid=${messageId} Error saving message:`,
        {
          error,
          errorType:
            error instanceof Error ? error.constructor.name : "Unknown",
          errorMessage:
            error instanceof Error ? error.message : "Unknown error",
          caseId: payload.caseId,
        }
      );
      // Not throwing error to ensure API response is still sent
    }

    return {
      statusCode: 200,
      body: JSON.stringify({
        message: "Message sent successfully",
        messageId: messageId,
        recipientNumber: payload.recipientNumber,
        messageType: payload.messageType,
      }),
    };
  }

  async processOutboundMessage(messageService: MessageService, payload: any) {
    console.log(
      `[ChatService][processOutboundMessage] tid=${payload.contextMessageId} Starting message processing:`,
      {
        recipientNumber: payload.recipientNumber,
        messageType: payload.messageType,
        messagePlatform: payload.messagePlatform,
        contextMessageId: payload.contextMessageId
          ? `wamid.${payload.contextMessageId}`
          : undefined,
        timestamp: new Date().toISOString(),
      }
    );

    try {
      const messageType = payload.messageType.toLowerCase();
      console.log(
        `[ChatService][processOutboundMessage] tid=${payload.contextMessageId} Normalized message type:`,
        {
          original: payload.messageType,
          normalized: messageType,
        }
      );

      let response;
      switch (messageType) {
        case MessageType.TEXT:
          console.log(
            `[ChatService][processOutboundMessage] tid=${payload.contextMessageId} Sending text message:`,
            {
              recipientNumber: payload.recipientNumber,
              textLength: payload.content.text.length,
              textBeingSent: payload.content.text,
              hasPreviewUrl: !!payload.content.previewUrl,
            }
          );
          response = await messageService.sendTextMessage(
            payload.recipientNumber,
            payload.content.text,
            {
              messageId: payload.contextMessageId,
              previewUrl: payload.content.previewUrl,
            },
            payload.messagePlatform
          );
          break;

        case MessageType.IMAGE:
          console.log(
            `[ChatService][processOutboundMessage] tid=${payload.contextMessageId} Sending image message:`,
            {
              recipientNumber: payload.recipientNumber,
              imageUrl: payload.content?.url,
              hasCaption: !!payload.content?.caption,
            }
          );
          response = await messageService.sendImageMessage(
            payload.recipientNumber,
            { url: payload.content.url },
            {
              messageId: payload.contextMessageId,
              caption: payload.content?.caption,
            },
            payload.messagePlatform
          );
          break;

        case MessageType.INTERACTIVE_REPLY:
          console.log(
            `[ChatService][processOutboundMessage] tid=${payload.contextMessageId} Sending interactive reply message:`,
            {
              recipientNumber: payload.recipientNumber,
              buttonCount: payload.content.buttons?.length,
              hasMedia: !!payload.content.media,
            }
          );
          response = await messageService.sendInteractiveMessage(
            payload.recipientNumber,
            {
              type: "button",
              body: {
                text: payload.content.body.text,
                ...(payload.content.body.caption && {
                  caption: payload.content.body.caption,
                }),
              },
              ...(payload.content.header && {
                header: payload.content.header,
              }),
              action: {
                buttons: payload.content.action.buttons,
              },
            },
            {
              messageId: payload.contextMessageId,
            },
            payload.messagePlatform
          );
          break;

        case MessageType.INTERACTIVE_LIST:
          console.log(
            `[ChatService][processOutboundMessage] tid=${payload.contextMessageId} Sending interactive list message:`,
            {
              recipientNumber: payload.recipientNumber,
              sectionCount: payload.content.sections?.length,
              hasHeader: !!payload.content.header,
            }
          );
          response = await messageService.sendInteractiveMessage(
            payload.recipientNumber,
            {
              type: "list",
              body: {
                text: payload.content.body.text,
                ...(payload.content.body.caption && {
                  caption: payload.content.body.caption,
                }),
              },
              ...(payload.content.header && {
                header: payload.content.header,
              }),
              action: payload.content.action,
            },
            {
              messageId: payload.contextMessageId,
            },
            payload.messagePlatform
          );
          break;

        default:
          console.error(
            `[ChatService][processOutboundMessage] tid=${payload.contextMessageId} Unsupported message type:`,
            {
              messageType,
              supportedTypes: Object.values(MessageType),
            }
          );
          throw new Error(`Unsupported message type: ${payload.messageType}`);
      }

      console.log(
        `[ChatService][processOutboundMessage] tid=${response?.messages?.[0]?.id} WhatsApp API response:`,
        {
          status: response?.status,
          timestamp: new Date().toISOString(),
        }
      );

      return response;
    } catch (error) {
      console.error(
        "[ChatService][processOutboundMessage] Error processing message:",
        {
          error,
          errorType:
            error instanceof Error ? error.constructor.name : "Unknown",
          errorMessage:
            error instanceof Error ? error.message : "Unknown error",
          errorStack: error instanceof Error ? error.stack : undefined,
          recipientNumber: payload.recipientNumber,
          messageType: payload.messageType,
          MessagePlatform: payload.messagePlatform,
          timestamp: new Date().toISOString(),
        }
      );

      throw error;
    }
  }

  async processImageUrl(source: MessagePlatform, message: any) {
    try {
      //---------------------------------<WHATSAPP IAMGE>---------------------------------//
      if (source === MessagePlatform.WHATSAPP) {
        const mediaId = message?.image?.id;
        const imageResponse =
          await this.messageService.processWhatsappImageMessage(mediaId);
        console.log(
          `[ChatService][processImageUrl] Processed image URL:`,
          imageResponse
        );
        return imageResponse;
      }
      //---------------------------------<CHATAPP URL>---------------------------------//
      if (source === MessagePlatform.NATIVE) {
        const imagePath = message?.image?.url;
        console.log(
          "[ChatService][processImageUrl] Processing image URL:",
          imagePath
        );
        //! NOTE  : This will be implemented when encoding and decoding images is cross platform campatible
        // const imageResponse = await this.imageStorageService.getDecodedImageUrl(
        //   imagePath
        // );
        const imageResponse = imagePath;
        console.log(
          "[ChatService][processImageUrl] Generated image URL:",
          imageResponse
        );

        return imageResponse;
      }
    } catch (error) {
      console.error(
        "[ChatService][processImageUrl] Error processing image URL:",
        error
      );
      throw error;
    }
  }
}