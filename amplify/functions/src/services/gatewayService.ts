import { MessageDao } from '../db/messageDao';
import { MessageOrigin, MessagePlatform, CaseStatus } from '../config/constants';
import { UserService } from "./userService";
import { CaseService } from "./caseService";
import { AIService } from "../ai/AzureAIIntegrationService";

export class GatewayService {
  private messageDao: MessageDao;
  private userService: UserService;
  private caseService: CaseService;
  private aiService: AIService;

  constructor() {
    console.log('Initializing GatewayService...');
    try {
      this.messageDao = new MessageDao();
      this.userService = new UserService();
      this.aiService = new AIService();
      this.caseService = new CaseService(this.aiService);
      console.log('GatewayService initialized successfully with services');
    } catch (error) {
      console.error('Error initializing GatewayService:', error);
      throw error;
    }
  }

  async getOrCreateUser(
    userId: string,
    userFullName: string,
    userWhatsAppNumber: string,
    messageId: string // Add messageId as a parameter
  ) {
    console.log(`[GatewayService][getOrCreateUser] tid=${messageId} called with:`, { userId, userFullName, userWhatsAppNumber });

    try {
      // First try to get the user
      console.log(`[GatewayService][getOrCreateUser] tid=${messageId} Attempting to get existing user:`, userId);
      const getUserResponse = await this.userService.getUserById(userId);
      console.log(`[GatewayService][getOrCreateUser] tid=${messageId} Get user response:`, getUserResponse);

      if (getUserResponse?.data) {
        console.log(`[GatewayService][getOrCreateUser] tid=${messageId} Existing user found:`, getUserResponse);
        return getUserResponse;
      }

      // If user doesn't exist, create new user
      console.log(`[GatewayService][getOrCreateUser] tid=${messageId} User not found, creating new user with details:`, {
        userId,
        userFullName,
        userWhatsAppNumber
      });
      const createResponse = await this.userService.createUser({
        userId,
        userFullName,
        userWhatsAppNumber
      });
      console.log(`[GatewayService][getOrCreateUser] tid=${messageId} Create user response:`, createResponse);

      if (!createResponse) {
        console.log(`[GatewayService][getOrCreateUser] tid=${messageId} [createUser] operation failed, response -> ${JSON.stringify(createResponse, null, 2)}`);
        throw new Error('createUser failed at gateway service');
      }

      return createResponse;
    } catch (error) {
      console.error(`[GatewayService][getOrCreateUser] tid=${messageId} Error in getOrCreateUser:`, {
        error,
        userId,
        userFullName,
        userWhatsAppNumber,
        errorMessage: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  async getActiveCase(userId: string, messageId: string) {
    console.log(`[GatewayService][getActiveCase] tid=${messageId} called for userId:`, userId);
    try {
      console.log(`[GatewayService][getActiveCase] tid=${messageId} Making request to case service for active case`);
      const response = await this.caseService.getActiveCase(userId);
      console.log(`[GatewayService][getActiveCase] tid=${messageId} Active case response:`, response);

      // Check if case is older than 1 hour
      if (response) {
        const lastUpdateTime = new Date(response.updatedAt).getTime();
        const currentTime = new Date().getTime();
        const oneHourInMs = 60 * 60 * 1000;

        if (currentTime - lastUpdateTime > oneHourInMs) {
          console.log(`[GatewayService][getActiveCase] tid=${messageId} Active case is older than 1 hour, closing case:`, response.caseId);
          await this.caseService.updateCase(userId, response.caseId, { caseStatus: CaseStatus.CLOSED });
          return null;
        }
      }

      return response;
    } catch (error) {
      console.error(`[GatewayService][getActiveCase] tid=${messageId} Error in getActiveCase:`, {
        error,
        userId,
        errorMessage: error instanceof Error ? error.message : 'Unknown error'
      });

      // Check if error is "Case not found"
      if (error instanceof Error &&
        (error.message.includes('Case not found') ||
          error.message.includes('Resource not found'))) {

        console.log(`[GatewayService][getActiveCase] tid=${messageId} No active case found, creating new case for user:`, userId);

        try {
          // Create new case
          const createResponse = await this.createNewCase(userId, messageId);

          console.log(`[GatewayService][getActiveCase] tid=${messageId} New case created successfully:`, {
            caseId: createResponse.caseId,
            userId,
            timestamp: new Date().toISOString()
          });

          return createResponse;

        } catch (createError) {
          console.error(`[GatewayService][getActiveCase] tid=${messageId} Error creating new case:`, {
            error: createError,
            userId,
            errorType: createError instanceof Error ? createError.constructor.name : 'Unknown',
            errorMessage: createError instanceof Error ? createError.message : 'Unknown error',
            timestamp: new Date().toISOString()
          });
          throw createError;
        }
      }

      // If it's a different error, throw it
      throw error;
    }
  }

  async createNewCase(userId: string, messageId: string) {
    console.log(`[GatewayService][createNewCase] tid=${messageId} called for userId:`, userId);
    try {
      console.log(`[GatewayService][createNewCase] tid=${messageId} Creating new case with params:`, {
        userId,
        caseStatus: CaseStatus.NEW,
        messages: []
      });
      const response = await this.caseService.createCase({
        userId,
        caseStatus: CaseStatus.NEW,
        messages: []
      });
      console.log('Create case response:', response);

      return response;
    } catch (error) {
      console.error('Error in createNewCase:', {
        error,
        userId,
        errorMessage: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  async storeMessage(messageData: {
    caseId: string;
    userId: string;
    messageId: string;
    messageType: "TEXT" | "IMAGE" | "INTERACTIVE";
    messageDirection: "INBOUND" | "OUTBOUND";
    messageContent: any;
    messagePlatform : MessagePlatform;
    messaageOrigin: MessageOrigin;
    messageContext?: any;
  }) {
    console.log(`[GatewayService][storeMessage] tid=${messageData.messageId} called with data:`, messageData);
    try {
      // First, verify case exists
      console.log(`[GatewayService][storeMessage] tid=${messageData.messageId} Verifying case exists:`, {
        caseId: messageData.caseId,
        userId: messageData.userId
      });
      const caseResponse = await this.caseService.getCaseById(messageData.userId, messageData.caseId);
      console.log(`[GatewayService][storeMessage] tid=${messageData.messageId} Case verification response:`, caseResponse);

      if (!caseResponse) {
        console.error(`[GatewayService][storeMessage] tid=${messageData.messageId} Case not found:`, messageData.caseId);
        throw new Error('Case not found');
      }

      // Store message in Messages table
      console.log(`[GatewayService][storeMessage] tid=${messageData.messageId} Storing message in Messages table`);
      await this.messageDao.createMessage(
        messageData.caseId,
        messageData.messageId,
        messageData.userId,
        messageData.messageDirection,
        'RECEIVED',
        messageData.messageType,
        messageData.messageContent,
        messageData.messagePlatform,
        messageData.messaageOrigin,
      );
      console.log(`[GatewayService][storeMessage] tid=${messageData.messageId} Message stored successfully in Messages table`);

      // Update case with message reference
      console.log(`[GatewayService][storeMessage] tid=${messageData.messageId} Updating case with message reference`);
      const updatedMessages = Array.isArray(caseResponse.messages)
        ? [...caseResponse.messages, messageData.messageId]
        : [messageData.messageId];
      await this.caseService.updateCase(messageData.userId, messageData.caseId, {
        messages: updatedMessages
      });
      console.log(`[GatewayService][storeMessage] tid=${messageData.messageId} Case updated with message reference successfully`);
    } catch (error) {
      console.error(`[GatewayService][storeMessage] tid=${messageData.messageId} Error in storeMessage:`, {
        error,
        messageData,
        errorMessage: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }
}
