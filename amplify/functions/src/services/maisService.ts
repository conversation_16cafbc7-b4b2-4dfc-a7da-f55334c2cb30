import { MessageDao } from "../db/messageDao";
import { AxiosClient } from "../config/axiosClient";

// UTILS
import { UseCaseUtils } from "../utils/maissUtils/useCaseUtils";
import { StyleProfileUtils } from "../utils/StyleProfileUtils";
import { CaseContextUtils } from "../utils/maissUtils/caseContextUtil";
import { OutfitUtils } from "../utils/maissUtils/outfitGenerationUtil";
import { OutfitReviewUtils } from "../utils/maissUtils/outfitReviewUtils";
import { AmaUtils } from "../utils/maissUtils/amaUtils";
import { ChatService } from "./chatService";
import { CaseService } from "./caseService";
import { UserService } from "./userService";
import { AIService } from "../ai/AzureAIIntegrationService";
import { TypeChatService } from "../ai/TypeChatService";

// enums
import {
  CaseStatus,
  MessagePlatform,
  UserStatus,
  UseCaseResponseType,
  MessageOrigin,
} from "../config/constants";

interface ProcessResponse {
  statusCode: number;
  body: string;
}

export class MAISService {
  private messageDao: MessageDao;
  private chatService: ChatService;
  private caseService: CaseService;
  private userService: UserService;
  private caseContextUtils: CaseContextUtils;
  private styleProfileClient: any;
  private aiService: AIService;
  private typeChatService: TypeChatService;

  constructor() {
    console.log("=== Initializing MAISService ===");
    try {
      this.messageDao = new MessageDao();
      this.userService = new UserService();
      this.aiService = new AIService();
      this.typeChatService = new TypeChatService();
      this.caseService = new CaseService(this.aiService);
      this.chatService = new ChatService(this.caseService);
      
      constructor() {
    console.log("=== Initializing MAISService ===");
    try {
      this.messageDao = new MessageDao();
      this.userService = new UserService();
      this.aiService = new AIService();
      this.typeChatService = new TypeChatService();
      this.caseService = new CaseService(this.aiService);
      this.chatService = new ChatService(this.caseService);
      
      this.styleProfileClient = AxiosClient.getInstance(
        "https://9w3knv1ar9.execute-api.us-east-1.amazonaws.com/dev"
      ).getClient();
      
      this.caseContextUtils = new CaseContextUtils(
        this.aiService,
        this.caseService,
        this.typeChatService,
        this.chatService
      );

      console.log(
        "[MIASService] MAISService initialized successfully with service classes"
      );
    } catch (error) {
      console.error("[MAISService] Error initializing MAISService:", {
        error,
        errorType: error instanceof Error ? error.constructor.name : "Unknown",
        errorMessage: error instanceof Error ? error.message : "Unknown error",
      });
      throw error;
    }
  }
      
      this.caseContextUtils = new CaseContextUtils(
        this.aiService,
        this.caseService,
        this.typeChatService,
        this.chatService
      );

      console.log(
        "[MIASService] MAISService initialized successfully with service classes"
      );
    } catch (error) {
      console.error("[MAISService] Error initializing MAISService:", {
        error,
        errorType: error instanceof Error ? error.constructor.name : "Unknown",
        errorMessage: error instanceof Error ? error.message : "Unknown error",
      });
      throw error;
    }
  }

  async processMessage(
    caseId: string,
    userId: string,
    messageId: string,
    messagePlatform: MessagePlatform
  ) {
    try {
      // Step 1 : Get Case Data
      const caseData = await this.verifyCaseAndGetData(
        caseId,
        userId,
        messageId
      );

      // Use UserService instead of userClient
      const userInfo = await this.userService.getUserById(userId);
      const userData = userInfo;

      console.log(
        `[MAISService][processMessage] tid=${messageId} Case and User Data Retrieved:`,
        {
          caseData,
          userInfo,
          userData,
        }
      );

      //Step 2 : Get Formatted and Sorted Messages
      const { sortedMessages, formattedMessages } =
        await this.getFormattedMessages(caseId, messageId);

      console.log(
        `[MAISService][processMessage] tid=${messageId} Messages and Case Data Retrieved:`,
        {
          sortedMessagesLength: sortedMessages?.length || 0,
          sortedMessages: sortedMessages?.[0],
          formattedMessagesLength: formattedMessages?.length || 0,
          formattedMessages: formattedMessages?.[0],
          caseData,
          userData,
        }
      );

      // Step 3: Process State Machine
      let result = await this.processState(
        caseData,
        userId,
        userData,
        caseId,
        messageId,
        formattedMessages as any[],
        sortedMessages as any[],
        messagePlatform
      );
      return result;
    } catch (error) {
      console.error(`[MAISService][processMessage] tid=${messageId} Error:`,
        {
          error,
          caseId,
          userId,
          messagePlatform,
          timestamp: new Date().toISOString(),
        }
      );
      throw error;
    }
  }

  private async processState(
    caseData: any,
    userId: string,
    userData: any,
    caseId: string,
    messageId: string,
    formattedMessages: any[],
    sortedMessages: any[],
    messagePlatform: MessagePlatform
  ): Promise<ProcessResponse> {
    // State 1: Use Case Identification
    if (caseData.caseStatus === CaseStatus.NEW) {
      const useCaseResult = await UseCaseUtils.handleCaseUsecaseSetup(
        caseId,
        userId,
        messageId,
        messagePlatform,
        false,
        formattedMessages
      );

      if (useCaseResult.type === UseCaseResponseType.INTENT_CLASSIFIED) {
        console.log(
          `[MAISService][processState] Use Case Identified:`,
          // This will still have the old use case or case status since local object is not updated
          caseData
        );
        return await this.processState(
          {
            ...caseData,
            caseStatus: CaseStatus.USECASE_IDENTIFIED,
            useCase: useCaseResult?.intent,
          },
          userId,
          userData,
          caseId,
          messageId,
          formattedMessages,
          sortedMessages,
          messagePlatform
        );
      }
      return {
        statusCode: 200,
        body: JSON.stringify(useCaseResult),
      };
    }

    if (caseData.caseStatus === CaseStatus.USECASE_IDENTIFIED) {
      console.log(`[MAISService][processState] User Data:`, userData);
      // Handle guest user name collection

      if (caseData.useCase === "AMA") {
        // handle Ai Chat
        const amaResponse = await AmaUtils.handleAmaChat(
          caseData,
          userId,
          caseId,
          messageId,
          messagePlatform,
          formattedMessages
        );

        if (amaResponse.type === "LIMIT_REACHED") {
          await this.handleCaseClose(userId, caseId);
        }

        if (amaResponse.type === "INTENT_SWITCH") {
          console.log(
            `[MAISService][processState] Intent switch detected:`,
            amaResponse
          );
          const parsedAmaResponse = JSON.parse(amaResponse.body);
          console.log(
            `[MAISService][processState] Updating case to new intent:`,
            parsedAmaResponse.intent
          );
          return await this.processState(
            {
              ...caseData,
              useCase: parsedAmaResponse?.intent,
            },
            userId,
            userData,
            caseId,
            messageId,
            formattedMessages,
            sortedMessages,
            messagePlatform
          );
        }

        return amaResponse;
      }

      // State 2: Style Profile Setup
      if (userData.userStatus === UserStatus.NEW) {
        console.log(
          `[MAISService][processState] User status is NEW, setting up style profile`
        );

        if (
          userData.userRole === "GUEST" &&
          userData.userFullName === "Guest User"
        ) {
          const lastOutboundMessage = sortedMessages.find(
            (m) =>
              m.messageDirection === "OUTBOUND" &&
              m.messageOrigin === "SYSTEM" &&
              m.messageContent.includes("Sure! But first")
          );

          if (lastOutboundMessage) {
            // Get the user's name response
            const nameResponse = sortedMessages[0];
            console.log(
              `[MAISService][processState] Name response:`,
              nameResponse
            );
            if (nameResponse.messageDirection === "INBOUND") {
              const name = JSON.parse(nameResponse.messageContent).body;

              // Update user data with name
              console.log(
                `[MAISService][processState] Updating user name:`,
                name
              );
              await this.userService.updateUser(userId, {
                userFullName: name,
              });

              console.log(
                `[MAISService][processState] User name updated successfully`
              );
              return this.processState(
                caseData,
                userId,
                { ...userData, userFullName: name },
                caseId,
                messageId,
                formattedMessages,
                sortedMessages,
                messagePlatform
              );
            }
          }

          // Send name request message
          await this.sendMessage(
            userId,
            caseId,
            "Sure! But first, what should I call you? 😊",
            messagePlatform,
            MessageOrigin.SYSTEM,
            messageId
          );

          return {
            statusCode: 200,
            body: JSON.stringify({
              type: "NAME_REQUESTED",
            }),
          };
        }

        const styleProfileResult =
          await StyleProfileUtils.handleStyleProfileSetup(
            caseId,
            caseData,
            userId,
            userData,
            sortedMessages.reverse(),
            messageId,
            messagePlatform,
            this.chatService,
            this.styleProfileClient
          );
        const parsedResult = JSON.parse(styleProfileResult.body);

        if (parsedResult.profileComplete) {
          const updatedUserData = {
            ...userData,
            userStatus: UserStatus.ONBOARDED,
          };

          await this.userService.updateUser(userId, {
            userStatus: UserStatus.ONBOARDED,
          });

          return await this.processState(
            {
              ...caseData,
              userProfile: parsedResult.profileData,
              caseStatus: CaseStatus.USECASE_IDENTIFIED,
            },
            userId,
            updatedUserData,
            caseId,
            messageId,
            formattedMessages,
            sortedMessages,
            messagePlatform
          );
        }
        return styleProfileResult;
      }

      if (!caseData.isProfileShown) {
        return this.handleProfileReview(
          caseData,
          userId,
          userData,
          caseId,
          messageId,
          messagePlatform,
          formattedMessages,
          sortedMessages
        );
      }
      // State 3: Case Context Gathering
      if (!caseData.contextReady) {
        console.log(
          `[MAISService][processState] Case Context Before Gathering:`,
          caseData
        );
        if (
          caseData?.useCase === "REVIEW_OUTFIT" &&
          !caseData?.caseContext?.userOutfitImage
        ) {
          const { userOutfitImage, shouldContinue } =
            await this.processOutfitReviewImage(
              userId,
              caseId,
              messageId,
              sortedMessages,
              messagePlatform
            );
          if (!shouldContinue) {
            return {
              statusCode: 200,
              body: JSON.stringify({
                message: "Image not found",
                caseStatus: caseData.caseStatus,
              }),
            };
          }
        }

        const contextResult = await this.caseContextUtils.gatherCaseContext(
          userId,
          sortedMessages,
          caseId,
          messageId,
          messagePlatform
        );
        const contextResultParsed = JSON.parse(contextResult.body);
        console.log(
          `[MAISService][processState] Context Result:`,
          contextResultParsed
        );
        if (JSON.parse(contextResult.body).contextReady) {
          return await this.processState(
            {
              ...caseData,
              caseContext: contextResultParsed.caseContext,
              caseStatus: CaseStatus.CONTEXT_GATHERED,
            },
            userId,
            userData,
            caseId,
            messageId,
            formattedMessages,
            sortedMessages,
            messagePlatform
          );
        }
        return contextResult;
      }
    }

    if (caseData.caseStatus === CaseStatus.CONTEXT_GATHERED) {
      let finalResponse;
      if (caseData.useCase === "REVIEW_OUTFIT") {
        finalResponse = await OutfitReviewUtils.reviewOutfit(
          caseData,
          userId,
          caseId,
          messageId,
          messagePlatform,
          caseData.caseContext.userOutfitImage
        );
      }
      if (caseData.useCase === "OUTFIT_CURATION") {
        finalResponse = await OutfitUtils.generateOutfit(
          caseData,
          userId,
          caseId,
          messageId,
          messagePlatform
        );
      }
      await this.handleCaseClose(userId, caseId);
    }

    // Final State
    return {
      statusCode: 200,
      body: JSON.stringify({
        message: "Process completed",
        caseStatus: caseData.caseStatus,
      }),
    };
  }

  private async verifyCaseAndGetData(
    caseId: string,
    userId: string,
    messageId: string
  ) {
    const caseResponse = await this.caseService.getCaseById(userId, caseId);

    if (!caseResponse || !caseResponse.data) {
      console.warn(
        `[MAISService][processMessage] tid=${messageId} Case not found:`,
        { caseId, userId }
      );
      throw new Error("Case not found");
    }

    console.log(
      `[MAISService][processMessage] tid=${messageId} Case verification response:`,
      {
        status: caseResponse.status,
        data: caseResponse.data,
      }
    );

    return caseResponse.data;
  }

  private async getFormattedMessages(caseId: string, messageId: string) {
    const messagesResponse = await this.messageDao.listMessagesByCase(caseId);
    console.log(
      `[MAISService][processMessage] tid=${messageId} Messages retrieved:`,
      {
        count: messagesResponse?.length || 0,
      },
      messagesResponse?.[messagesResponse.length - 1]
    );

    const sortedMessages = messagesResponse?.sort(
      (a, b) =>
        new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
    );

    const formattedMessages = sortedMessages?.map((message) => ({
      messageDirection: message.messageDirection,
      messageOrigin: message.messageOrigin,
      messageContent: message.messageContent,
    }));

    return { sortedMessages, formattedMessages };
  }

  private async sendMessage(
    userId: string,
    caseId: string,
    message: string,
    messagePlatform: MessagePlatform,
    messaageOrigin: MessageOrigin,
    messageId: string
  ) {
    console.log(
      `[UseCaseUtils][sendMessage] tid=${messageId} Sending ${messagePlatform} message:`,
      { userId, message }
    );
    try {
      await this.chatService.sendMessage({
        recipientNumber: userId, // WhatsApp number is the userId
        messageType: "TEXT",
        messagePlatform: messagePlatform,
        content: {
          text: message,
        },
        messageOrigin: messaageOrigin,
        caseId,
      });

      console.log(
        `[UseCaseUtils][sendMessage] tid=${messageId} ${messagePlatform} message sent successfully:`,
        {
          recipientNumber: userId,
          message,
          messagePlatform,
        }
      );
    } catch (error) {
      console.error(
        `[UseCaseUtils][sendMessage] tid=${messageId} Error sending ${messagePlatform} message:`,
        {
          error,
          userId,
          message,
          messagePlatform,
          errorMessage:
            error instanceof Error ? error.message : "Unknown error",
        }
      );
      throw error;
    }
  }

  private async processOutfitReviewImage(
    userId: string,
    caseId: string,
    messageId: string,
    sortedMessages: any[],
    messagePlatform: MessagePlatform
  ): Promise<{ userOutfitImage?: string; shouldContinue: boolean }> {
    console.log(
      `[MAISService][processOutfitReviewImage] tid=${messageId} Processing latest message for image URL`
    );

    if (!sortedMessages || sortedMessages.length === 0) {
      console.log(
        `[MAISService][processOutfitReviewImage] tid=${messageId} No messages found`
      );
      return { shouldContinue: false };
    }
    console.log(
      `[MAISService][processOutfitReviewImage] tid=${messageId} Sorted Messages:`,
      sortedMessages
    );

    // Get latest message
    const latestMessage = sortedMessages[sortedMessages.length - 1];
    console.log(
      `[MAISService][processOutfitReviewImage] tid=${messageId} Latest Message:`,
      latestMessage
    );

    if (latestMessage.messageType?.toUpperCase() === "IMAGE") {
      try {
        const content =
          typeof latestMessage.messageContent === "string"
            ? JSON.parse(latestMessage.messageContent)
            : latestMessage.messageContent;

        console.log(
          `[MAISService][processOutfitReviewImage] tid=${messageId} Parsed Content:`,
          content
        );

        if (content.url) {
          console.log(
            `[MAISService][processOutfitReviewImage] tid=${messageId} Image URL found in latest message:`,
            content.url
          );

          console.log(
            `[MAISService][processOutfitReviewImage] tid=${messageId} Updating case with image url:`
          );
          // update case with image url
          await this.caseService.updateCase(userId, caseId, {
            caseContext: {
              userOutfitImage: content.url,
            },
          });

          return { userOutfitImage: content.url, shouldContinue: true };
        }
      } catch (error) {
        console.error(
          `[MAISService][processOutfitReviewImage] tid=${messageId} Error processing message content:`,
          error
        );
      }
    }

    // If we get here, no image URL was found
    console.log(
      `[MAISService][processOutfitReviewImage] tid=${messageId} No image URL found, sending request message`
    );

    await this.sendMessage(
      userId,
      caseId,
      "Please share a photo of the outfit you'd like me to review! 📸",
      messagePlatform,
      MessageOrigin.SYSTEM,
      messageId
    );
    return { userOutfitImage: undefined, shouldContinue: false };
  }

  private async handleProfileReview(
    caseData: any,
    userId: string,
    userData: any,
    caseId: string,
    messageId: string,
    messagePlatform: MessagePlatform,
    formattedMessages: any[],
    sortedMessages: any[]
  ): Promise<ProcessResponse> {
    try {
      console.log(
        `[MAISService][handleProfileReview] tid=${messageId} Starting profile review`,
        {
          userId,
          caseId,
          messageId,
        }
      );

      // Get latest message to check for button response
      const lastMessage = sortedMessages[sortedMessages.length - 1];

      // Check if this is a response to our profile review specifically
      if (lastMessage?.messageType === "INTERACTIVE") {
        const messageContent = JSON.parse(lastMessage.messageContent);
        const response = messageContent.button_reply;

        console.log(
          `[MAISService][handleProfileReview] tid=${messageId} Processing interactive message:`,
          {
            messageContent,
            response,
          }
        );

        // Only process if it's a response to our profile review buttons
        if (response?.id === "PERFECT" || response?.id === "UPDATE") {
          // Update isProfileShown to true regardless of response
          await this.caseService.updateCase(userId, caseId, {
            isProfileShown: true,
          });

          console.log(
            `[MAISService][handleProfileReview] tid=${messageId} Profile review response:`,
            {
              responseId: response.id,
            }
          );

          if (response.id === "PERFECT") {
            const updatedUserData = {
              ...userData,
              userStatus: UserStatus.ONBOARDED,
            };
            // Continue to context gathering
            return this.processState(
              {
                ...caseData,
                isProfileShown: true,
              },
              userId,
              updatedUserData,
              caseId,
              messageId,
              formattedMessages,
              sortedMessages,
              messagePlatform
            );
          } else {
            // Update user status to NEW
            console.log(
              `[MAISService][handleProfileReview] tid=${messageId} Updating user status to NEW`,
              {
                userId,
                caseId,
                messageId,
              }
            );

            await this.userService.updateUser(userId, {
              userStatus: UserStatus.NEW,
            });

            // Update the user style profile with the new API
            console.log(
              `[MAISService][handleProfileReview] tid=${messageId} Updating user style profile`
            );

            try {
              await StyleProfileUtils.updateUserStyleProfile(
                userId,
                {
                  userGender: null,
                  userAge: null,
                  userUndertone: null,
                  userBodyType: null,
                },
                messageId,
                this.styleProfileClient
              );

              console.log(
                `[MAISService][handleProfileReview] tid=${messageId} User style profile updated successfully:`,
                {}
              );
            } catch (error) {
              console.error(
                `[MAISService][handleProfileReview] tid=${messageId} Error updating style profile:`,
                {
                  error,
                  errorType:
                    error instanceof Error ? error.constructor.name : "Unknown",
                  errorMessage:
                    error instanceof Error ? error.message : "Unknown error",
                }
              );
              // Continue even if update fails, as the profile might not exist
            }

            const updatedUserData = {
              ...userData,
              userStatus: UserStatus.NEW,
            };

            // Reset to NEW status to redo profile
            return await this.processState(
              {
                ...caseData,
                isProfileShown: true,
              },
              userId,
              updatedUserData,
              caseId,
              messageId,
              formattedMessages,
              sortedMessages,
              messagePlatform
            );
          }
        }
      }

      console.log(
        `[MAISService][handleProfileReview] tid=${messageId} Fetching user profile from new API`
      );

      // Send the profile review message
      let userProfile;
      try {
        userProfile = await this.styleProfileClient?.get(
          "/style-profile",
          {
            params: { userId },
            headers: {
              Authorization: "Bearer SYSTEM", // Update with the correct token
            },
          }
        );

        console.log(
          `[MAISService][handleProfileReview] tid=${messageId} Profile response:`,
          userProfile?.data
        );

        userProfile = userProfile?.data?.data;
      } catch (error) {
        console.error(
          `[MAISService][handleProfileReview] tid=${messageId} Error fetching profile:`,
          {
            error,
            errorType:
              error instanceof Error ? error.constructor.name : "Unknown",
            errorMessage:
              error instanceof Error ? error.message : "Unknown error",
          }
        );
        throw new Error("Failed to fetch user profile");
      }

      if (userProfile) {
        // Format the profile data for display
        const formatDisplay = (
          value: string | undefined,
          defaultValue: string = "Not specified"
        ): string => {
          if (!value) return defaultValue;
          return value.charAt(0).toUpperCase() + value.slice(1).toLowerCase();
        };

        // For body type, map from internal value to display value if needed
        const getBodyTypeDisplay = (bodyType: string | undefined): string => {
          if (!bodyType) return "Not specified";

          // Convert body type to display format
          return (
            bodyType.charAt(0).toUpperCase() + bodyType.slice(1).toLowerCase()
          );
        };

        // Format age range for display
        const formatAgeRange = (ageRange: string | undefined): string => {
          if (!ageRange) return "Not specified";

          if (ageRange === "AGE_0_20") return "<20";
          if (ageRange === "AGE_55_99") return "55+";

          // For other age ranges, convert format from AGE_XX_YY to XX-YY
          return ageRange.replace("AGE_", "").replace(/_/g, "-");
        };

        const gender = formatDisplay(userProfile.userGender);
        const bodyType = getBodyTypeDisplay(userProfile.userBodyType);
        const skinTone = formatDisplay(userProfile.userUndertone);
        const age = formatAgeRange(userProfile.userAge);

        console.log(
          `[MAISService][handleProfileReview] tid=${messageId} Sending profile review message`,
          {
            skinTone,
            gender,
            bodyType,
            age,
          }
        );

        await this.sendMessage(
          userId,
          caseId,
          `Here's your style profile summary:\n\n1) Gender: ${gender}\n2) Body Type: ${bodyType}\n3) Skin Tone: ${skinTone}\n4) Age Range: ${age} Years\n\nDoes this look correct?`,
          messagePlatform,
          MessageOrigin.SYSTEM,
          messageId
        );

        return {
          statusCode: 200,
          body: JSON.stringify({
            message: "Profile review message sent",
            awaitingResponse: true,
          }),
        };
      }

      // If user profile is not found
      console.log(
        `[MAISService][handleProfileReview] tid=${messageId} User profile not found`
      );
      throw new Error("User profile not found");
    } catch (error) {
      console.error(
        `[MAISService][handleProfileReview] tid=${messageId} Error:`,
        {
          error,
          caseId,
          userId,
          errorType:
            error instanceof Error ? error.constructor.name : "Unknown",
          errorMessage:
            error instanceof Error ? error.message : "Unknown error",
        }
      );
      throw error;
    }
  }

  private async handleCaseClose(userId: string, caseId: string) {
    try {
      console.log(`[MAISService][handleCaseUpdate] Closing case:`, {
        userId,
        caseId,
      });
      await this.caseService.closeCase(userId, caseId);
      console.log(`[MAISService][handleCaseUpdate] Case closed successfully:`, {
        userId,
        caseId,
      });
    } catch (error) {
      console.error(`[MAISService][handleCaseUpdate] Error:`, {
        error,
        caseId,
        userId,
        errorMessage: error instanceof Error ? error.message : "Unknown error",
      });
      throw error;
    }
  }
}
