import { Amplify } from "aws-amplify";
import outputs from "../../../../amplify_outputs.json";
Amplify.configure(outputs);

import { OutfitDao } from "../db/outfitDao";
import {
  Outfit,
  CreateOutfitInput,
  UpdateOutfitInput,
  ListOutfitsInput,
  ListOutfitsOutput,
  UpdateOutfitOutput,
} from "../functions/outfitController/types";

export class OutfitService {
  private outfitDao: OutfitDao;

  constructor() {
    this.outfitDao = new OutfitDao();
  }

  async createOutfit(input: CreateOutfitInput): Promise<Outfit> {
    console.log("[OutfitService][createOutfit] Creating outfit:", input);

    try {
      return await this.outfitDao.createOutfit(input);
    } catch (error) {
      console.error("[OutfitService][createOutfit] Error:", error);
      throw error;
    }
  }

  async getOutfitById(userId: string, outfitId: string): Promise<Outfit> {
    console.log("[OutfitService][getOutfitById] Getting outfit:", {
      userId,
      outfitId,
    });

    try {
      const outfit = await this.outfitDao.getOutfitById(userId, outfitId);
      if (!outfit) {
        throw new Error("Outfit not found");
      }
      return outfit;
    } catch (error) {
      console.error("[OutfitService][getOutfitById] Error:", error);
      throw error;
    }
  }

  async listOutfits(input: ListOutfitsInput): Promise<ListOutfitsOutput> {
    console.log("[OutfitService][listOutfits] Listing outfits:", input);

    try {
      return await this.outfitDao.listOutfits(input);
    } catch (error) {
      console.error("[OutfitService][listOutfits] Error:", error);
      throw error;
    }
  }

  async updateOutfit(
    userId: string,
    outfitId: string,
    updates: UpdateOutfitInput
  ): Promise<UpdateOutfitOutput> {
    console.log("[OutfitService][updateOutfit] Updating outfit:", {
      userId,
      outfitId,
      updates,
    });

    try {
      const existingOutfit = await this.outfitDao.getOutfitById(
        userId,
        outfitId
      );
      if (!existingOutfit) {
        throw new Error("Outfit not found");
      }
      return await this.outfitDao.updateOutfit(userId, outfitId, updates);
    } catch (error) {
      console.error("[OutfitService][updateOutfit] Error:", error);
      throw error;
    }
  }

  async deleteOutfit(userId: string, outfitId: string): Promise<void> {
    console.log("[OutfitService][deleteOutfit] Deleting outfit:", {
      userId,
      outfitId,
    });

    try {
      const existingOutfit = await this.outfitDao.getOutfitById(
        userId,
        outfitId
      );
      if (!existingOutfit) {
        throw new Error("Outfit not found");
      }
      await this.outfitDao.deleteOutfit(userId, outfitId);
    } catch (error) {
      console.error("[OutfitService][deleteOutfit] Error:", error);
      throw error;
    }
  }
}
