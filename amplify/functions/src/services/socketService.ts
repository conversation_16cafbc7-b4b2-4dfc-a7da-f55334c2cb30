import { SocketDao } from "../db/socketDao";
import { AuthService } from "./authService";
import { ImageStorageService } from "../integrations/ImageStorageService";
import { ChatService } from "./chatService";

export class SocketService {
  private chatService: ChatService;
  private socketDao: SocketDao;
  private authService: AuthService;
  private imageStorageService: ImageStorageService;

  constructor() {
    this.chatService = new ChatService();
    this.socketDao = new SocketDao();
    this.authService = new AuthService();
    this.imageStorageService = new ImageStorageService();
  }

  async verifyAuthToken(userId: string, authToken: string) {
    console.log(`[SOCKET][SERVICE] Verifying Auth Token for userId: ${userId}`);
    try {
      const response = await this.authService.verifyAuthToken(authToken);
      console.log("[SOCKET][SERVICE] Token verification response:", response);
      return response;
    } catch (error) {
      console.error("Token verification failed:", error);
      return false;
    }
  }

  async getConnection(userId: string) {
    console.log(`[SOCKET][SERVICE] Getting connection for userId: ${userId}`);
    return await this.socketDao.getConnection(userId);
  }

  async updateConnection(
    userId: string,
    connectionId: string,
    createdAt: string
  ) {
    console.log(`[SOCKET][SERVICE] Updating connection for userId: ${userId}`, {
      connectionId,
      createdAt,
    });
    return await this.socketDao.updateConnection(
      userId,
      connectionId,
      createdAt
    );
  }

  async createConnection(
    userId: string,
    connectionId: string,
    createdAt: string
  ) {
    console.log(`[SOCKET][SERVICE] Creating connection for userId: ${userId}`, {
      connectionId,
      createdAt,
    });
    return await this.socketDao.createConnection(
      userId,
      connectionId,
      createdAt
    );
  }

  async deleteConnection(userId: string) {
    console.log(`[SOCKET][SERVICE] Deleting connection for userId: ${userId}`);
    return await this.socketDao.deleteConnection(userId);
  }

  async sendMessage(payload: any) {
    console.log("[SOCKET]{SERVICE] Sending message via ChatService", payload);
    try {
      const response = await this.chatService.sendMessage(payload);
      console.log(
        "[SOCKET][SERVICE] Message Sent Successfully:",
        response
      );
      return response;
    } catch (error) {
      console.error("[SOCKET][SERVICE] Error:", error);
      throw error;
    }
  }
}
