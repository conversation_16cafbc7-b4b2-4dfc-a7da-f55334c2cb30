import { UserInfoDao } from "../db/userInfoDao";
import { UserStyleProfileDao } from "../db/userStyleProfileDao";
import { UserStatus } from "../config/constants";

import jwt, { SignOptions, Secret } from "jsonwebtoken";

interface CreateUserInput {
  userId: string;
  userFullName: string;
  userWhatsAppNumber: string;
}

interface UpdateUserInput {
  userFullName?: string;
  userWhatsAppNumber?: string;
  userStatus?: UserStatus;
}

export class UserService {
  private userInfoDao: UserInfoDao;
  private userStyleProfileDao: UserStyleProfileDao;
  private TOKEN_EXPIRY: SignOptions["expiresIn"] = "30d";
  private JWT_SECRET: Secret = "SECRET_KEY";

  constructor() {
    this.userInfoDao = new UserInfoDao();
    this.userStyleProfileDao = new UserStyleProfileDao();
  }

  async createUser(input: CreateUserInput) {
    //! NOTE : TEMP TOkEN GENERATION SO THAT A USER CAN BE CREATED
    const tempToken = jwt.sign(input, this.JWT_SECRET, {
      expiresIn: this.TOKEN_EXPIRY,
    });
    return await this.userInfoDao.createUserInfo(
      input.userId,
      input.userFullName,
      input.userWhatsAppNumber,
      UserStatus.NEW,
      // We use this createUserInfo method only when we recieve a message from WA 
      // And since we are not using the token for any verification, we can use a temp token
      //! Any user coming from chatapp will first have to go through auth therefore the user will be created there
      //! This is just a temp solution and shall be updated in future for better security
      "USER",
      tempToken
    );
  }

  async updateUser(userId: string, updates: UpdateUserInput) {
    return await this.userInfoDao.updateUserInfo(userId, updates);
  }

  async getUserById(userId: string) {
    return await this.userInfoDao.getUserInfoById(userId);
  }

  async getUserStyleProfile(userId: string) {
    console.log("Getting style profile for user:", userId);
    try {
      const user = await this.userStyleProfileDao.getUserStyleProfileById(
        userId
      );
      console.log("User style profile retrieved:", user);

      if (!user) {
        console.warn("Style profile not found for user:", userId);
        return null;
      }

      // Transform the data into a style profile format
      const styleProfile = {
        userId: user.data?.userId,
        userBodyType: user.data?.userBodyType,
        userSkinUnderTone: user.data?.userSkinUnderTone,
        userAge: user.data?.userAge,
      };

      console.log("Style profile compiled:", styleProfile);
      return styleProfile;
    } catch (error) {
      console.error("Error getting user style profile:", {
        error,
        userId,
        errorMessage: error instanceof Error ? error.message : "Unknown error",
      });
      throw error;
    }
  }
}
