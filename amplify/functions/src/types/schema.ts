// schemas.ts
export interface IntentResponse {
  classifiedIntent: "REVIEW_OUTFIT" | "OUTFIT_CURATION" | "AMA" | null;
  followUpMessage: string | null;
}

export interface CaseContextResponse {
  eventType: string | null;
  eventTime:
    | "EARLY MORNING"
    | "MORNING"
    | "AFTERNOON"
    | "EVENING"
    | "NIGHT"
    | null;
}

export interface CurationCaseContextResponse extends CaseContextResponse {
  eventOutfitVibe:
    | "BOLD"
    | "PLAYFUL"
    | "CASUAL"
    | "EDGY"
    | "MINIMAL"
    | "ETHNIC"
    | "BOHEMIAN"
    | "SPORTY"
    | "ELEGANT"
    | "PROFESSIONAL"
    | "SURPRISE_ME"
    | null;
}

export interface ReviewCaseContextResponse extends CaseContextResponse {}

export interface EventOptionsResponse {
  options: string[];
}

export interface OutfitResponse {
  TopWear: string;
  BottomWear: string;
  Accessories: string;
  Shoes: string;
}