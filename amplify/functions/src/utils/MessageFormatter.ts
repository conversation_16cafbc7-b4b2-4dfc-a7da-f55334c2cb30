import { MessageOrigin } from "../config/constants";

interface Message {
  messageDirection: string;
  messageOrigin: string;
  messageContent: string;
}

export class MessageFormatter {
  /**
   * Formats a single message into the required format
   * @param message The message object to format
   * @returns Formatted message string
   */
  static formatSingleMessage(message: Message): string {
    try {
      const direction = message.messageDirection;
      const messageOrigin = message.messageOrigin;
      let content = message.messageContent;

      // Try to parse the content if it's a JSON string
      try {
        const parsedContent = JSON.parse(content);

        if (messageOrigin === MessageOrigin.SYSTEM) {
          console.log("System message detected, Returning NULL", parsedContent);
          return "";
        }
        if (parsedContent?.body) {
          console.log("Body detected, returning body", parsedContent);
          content = parsedContent.body;
        } else if (parsedContent?.caption) {
          console.log("Caption detected, returning caption", parsedContent);
          content = parsedContent.caption;
        } else if (parsedContent?.text) {
          console.log("Text detected, returning text", parsedContent);
          content = parsedContent.text;
        } else if (parsedContent?.type) {
          //! REMOVING ONBOARDING MESSAGES
          console.log(
            "Onboarding message detected, Returning NULL",
            parsedContent
          );
          return "";
        } else {
          //! NORMAL AI MESSAGE
          console.log(
            "Normal AI message detected, skipping formatting",
            parsedContent
          );
          content = JSON.stringify(parsedContent);
        }
      } catch (e) {
        console.log("Error parsing message content", e);
      }
      console.log("Returning formatted message", `${direction}: ${content}`);
      return `${direction}: ${content}`;
    } catch (error) {
      console.error("Error formatting message:", error);
      return ""; // Return empty string in case of error
    }
  }

  /**
   * Formats an array of messages
   * @param messages Array of messages to format
   * @returns Array of formatted message strings
   */
  static formatMessages(messages: Message[]): string[] {
    return messages
      .map((msg) => this.formatSingleMessage(msg))
      .filter((msg) => msg !== ""); // Remove any empty messages from failed formatting
  }

  /**
   * Gets plain content from a message without direction prefix
   * @param message The message object to extract content from
   * @returns The plain content string
   */
  static getPlainContent(message: Message): string {
    try {
      let content = message.messageContent;

      try {
        const parsedContent = JSON.parse(content);
        if (parsedContent.body) {
          return parsedContent.body;
        } else if (parsedContent.caption) {
          return parsedContent.caption;
        }
        return content;
      } catch (e) {
        return content;
      }
    } catch (error) {
      console.error("Error getting plain content:", error);
      return "";
    }
  }
}

// Example usage:
/*
const messages = [
  {
    messageDirection: 'INBOUND',
    messageOrigin: 'USER',
    messageContent: '{"body":"ha ha"}'
  },
  {
    messageDirection: 'OUTBOUND',
    messageOrigin: 'MODEL',
    messageContent: 'Hello there!'
  },
  {
    messageDirection: 'INBOUND',
    messageOrigin: 'USER',
    messageContent: '{"url":"https://example.com/image.jpg","caption":"Hey bro"}'
  }
];

const formatted = MessageFormatter.formatMessages(messages);
Results in:
["INBOUND: ha ha", "OUTBOUND: Hello there!", "INBOUND: Hey bro"]
*/
