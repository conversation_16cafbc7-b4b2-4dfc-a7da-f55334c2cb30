import { AxiosClient } from "../config/axiosClient";
import {
  MessageDirection,
  MessageOrigin,
  MessagePlatform,
  UserRole,
} from "../config/constants";
import { UserInfoDao } from "../db/userInfoDao";
import { ChatService } from "../services/chatService";

// API URL for the new style profile service
const STYLE_PROFILE_API_URL =
  "https://9w3knv1ar9.execute-api.us-east-1.amazonaws.com/dev";

type Gender = "MASCULINE" | "FEMININE";
type BodyTypeCode = "O" | "H" | "A" | "Y" | "X";
// Body type mapping according to gender
const BODY_TYPE_MAPPING: Record<Gender, Record<BodyTypeCode, string>> = {
  MASCULINE: {
    O: "OVAL",
    H: "RECTANGLE",
    A: "TRIANGLE",
    Y: "INVERTED_TRIANGLE",
    X: "TRAPEZOID",
  },
  FEMININE: {
    O: "APPLE",
    H: "RECTANGLE",
    A: "PEAR",
    Y: "INVERTED_TRIANGLE",
    X: "HOURGLASS",
  },
};

export class StyleProfileUtils {
  private static async processInteractiveResponse(
    lastMessage: any,
    userId: string,
    userData: any,
    caseId: string,
    caseData: any,
    messageId: string,
    messagePlatform: MessagePlatform,
    chatService: ChatService,
    styleProfileClient: any
  ) {
    if (!lastMessage) {
      console.log(
        `[StyleProfileUtils][processInteractiveResponse] lastMessage received is null, lastMessage -> ${lastMessage}`
      );
      return null;
    }

    console.log(
      `[StyleProfileUtils][processInteractiveResponse] tid=${messageId} Processing interactive response:`,
      {
        userId,
        messageType: lastMessage?.messageType,
      }
    );

    if (lastMessage?.messageType !== "INTERACTIVE") {
      console.log(
        `[StyleProfileUtils][processInteractiveResponse] tid=${messageId} Not an interactive message, skipping`
      );
      return null;
    }

    const messageContent = JSON.parse(lastMessage.messageContent);

    console.log(
      `[StyleProfileUtils][processInteractiveResponse] tid=${messageId} lastMessage messageContent -> ${JSON.stringify(
        messageContent,
        null,
        2
      )}`
    );

    const responseType = messageContent.type; // 'button_reply' or 'list_reply' or 'input_reply'
    const response = (() => {
      switch (responseType) {
        case "button_reply":
          return messageContent.button_reply;
        case "list_reply":
          return messageContent.list_reply;
        case "input_reply":
          return messageContent.input_reply;
        default:
          return null;
      }
    })();

    console.log(
      `[StyleProfileUtils][processInteractiveResponse] tid=${messageId} Interactive response details:`,
      {
        responseType,
        responseId: response?.id,
        responseText: response?.text, // For input_reply
        responseTitle: response?.title, // For button/list reply
      }
    );

    try {
      // Fetch current profile
      console.log(
        `[StyleProfileUtils][processInteractiveResponse] tid=${messageId} Fetching user style profile for userId: ${userId}`
      );

      let currentProfileResponse;
      try {
        currentProfileResponse = await styleProfileClient.get(
          "/style-profile",
          {
            params: { userId },
            headers: {
              Authorization: "Bearer SYSTEM", // Update with the correct token
            },
          }
        );
        console.log(
          `[StyleProfileUtils][processInteractiveResponse] tid=${messageId} Got profile response:`,
          currentProfileResponse?.data
        );
      } catch (error) {
        console.log(
          `[StyleProfileUtils][processInteractiveResponse] tid=${messageId} No existing profile found, will create new one if needed`,
          error
        );
        // Profile doesn't exist yet, that's okay
      }

      const currentProfile = currentProfileResponse?.data?.data;

      if (["MASCULINE", "FEMININE"].includes(response.id)) {
        // Gender response
        console.log(
          `[StyleProfileUtils][processInteractiveResponse] tid=${messageId} Processing gender response:`,
          {
            userId,
            gender: response.id,
          }
        );

        // Create or update profile with gender
        if (!currentProfile) {
          // Create new profile with gender
          const createResponse = await styleProfileClient.post(
            "/style-profile",
            {
              userId,
              userGender: response.id,
            },
            {
              headers: {
                Authorization: "Bearer SYSTEM", // Update with the correct token
              },
            }
          );

          console.log(
            `[StyleProfileUtils][processInteractiveResponse] tid=${messageId} Created new profile:`,
            createResponse?.data
          );
        } else {
          // Update existing profile with gender
          const updateResponse = await styleProfileClient.put(
            "/style-profile",
            {
              userId,
              updates: {
                userGender: response.id,
              },
            },
            {
              headers: {
                Authorization: "Bearer SYSTEM", // Update with the correct token
              },
            }
          );

          console.log(
            `[StyleProfileUtils][processInteractiveResponse] tid=${messageId} Updated profile with gender:`,
            updateResponse?.data
          );
        }

        // Proceed to ask body type
        return await StyleProfileUtils.askBodyType(
          userId,
          caseId,
          response.id,
          messageId,
          messagePlatform,
          chatService
        );
      } else if (["O", "H", "A", "Y", "X"].includes(response.id)) {
        // Body type response
        console.log(
          `[StyleProfileUtils][processInteractiveResponse] tid=${messageId} Processing body type response:`,
          {
            userId,
            bodyTypeCode: response.id,
          }
        );

        const gender = (currentProfile?.userGender || "MASCULINE") as Gender;
        const bodyTypeCode = response.id as BodyTypeCode;
        const mappedBodyType = BODY_TYPE_MAPPING[gender][bodyTypeCode];

        console.log(
          `[StyleProfileUtils][processInteractiveResponse] tid=${messageId} Mapped body type:`,
          {
            code: response.id,
            gender,
            mappedTo: mappedBodyType,
          }
        );

        // Update profile with mapped body type
        const updateResponse = await styleProfileClient.put(
          "/style-profile",
          {
            userId,
            updates: {
              userBodyType: mappedBodyType,
            },
          },
          {
            headers: {
              Authorization: "Bearer SYSTEM", // Update with the correct token
            },
          }
        );

        console.log(
          `[StyleProfileUtils][processInteractiveResponse] tid=${messageId} Updated profile with body type:`,
          updateResponse?.data
        );

        // Proceed to ask skin tone/undertone
        return await StyleProfileUtils.askSkinTone(
          userId,
          caseId,
          messageId,
          messagePlatform,
          chatService
        );
      } else if (["WARM", "COOL"].includes(response.id)) {
        // Undertone response
        console.log(
          `[StyleProfileUtils][processInteractiveResponse] tid=${messageId} Processing undertone response:`,
          {
            userId,
            undertone: response.id,
          }
        );

        // Update profile with undertone
        const updateResponse = await styleProfileClient.put(
          "/style-profile",
          {
            userId,
            updates: {
              userUndertone: response.id,
            },
          },
          {
            headers: {
              Authorization: "Bearer SYSTEM", // Update with the correct token
            },
          }
        );

        console.log(
          `[StyleProfileUtils][processInteractiveResponse] tid=${messageId} Updated profile with undertone:`,
          updateResponse?.data
        );

        // Proceed to ask age range
        return await StyleProfileUtils.askAgeRange(
          userId,
          caseId,
          messageId,
          messagePlatform,
          chatService
        );
      } else if (response.id.startsWith("AGE_")) {
        // Age range response
        console.log(
          `[StyleProfileUtils][processInteractiveResponse] tid=${messageId} Processing age range response:`,
          {
            userId,
            ageRange: response.id,
          }
        );

        // Update profile with age range
        const updateResponse = await styleProfileClient.put(
          "/style-profile",
          {
            userId,
            updates: {
              userAge: response.id,
            },
          },
          {
            headers: {
              Authorization: "Bearer SYSTEM", // Update with the correct token
            },
          }
        );

        console.log(
          `[StyleProfileUtils][processInteractiveResponse] tid=${messageId} Updated profile with age:`,
          updateResponse?.data
        );

        // Profile complete
        return {
          statusCode: 200,
          body: JSON.stringify({
            message: "Style profile completed",
            profileComplete: true,
          }),
        };
      }

      console.log(
        `[StyleProfileUtils][processInteractiveResponse] tid=${messageId} Unrecognized response ID:`,
        {
          userId,
          responseId: response.id,
        }
      );
      return null;
    } catch (error) {
      console.error(
        `[StyleProfileUtils][processInteractiveResponse] tid=${messageId} Error processing response:`,
        {
          error,
          userId,
          responseId: response.id,
          errorType:
            error instanceof Error ? error.constructor.name : "Unknown",
          errorMessage:
            error instanceof Error ? error.message : "Unknown error",
        }
      );
      throw error;
    }
  }

  public static async handleStyleProfileSetup(
    caseId: string,
    caseData: any,
    userId: string,
    userData: any,
    messages: any[],
    messageId: string,
    messagePlatform: MessagePlatform,
    chatService: ChatService,
    styleProfileClient: any
  ) {
    console.log(
      `[StyleProfileUtils][handleStyleProfileSetup] tid=${messageId} Starting style profile setup:`,
      {
        caseId,
        userId,
        messageCount: messages.length,
      }
    );

    console.log(
      `[StyleProfileUtils][handleStyleProfileSetup] tid=${messageId} Full messages received:\n`,
      JSON.stringify(messages, null, 2)
    );

    try {
      // Get the last message
      const lastMessage = messages[messages.length - 1];

      // Try to process interactive response first
      const interactiveResult =
        await StyleProfileUtils.processInteractiveResponse(
          lastMessage,
          userId,
          userData,
          caseId,
          caseData,
          messageId,
          messagePlatform,
          chatService,
          styleProfileClient
        );
      if (interactiveResult) {
        return interactiveResult;
      }

      console.log(
        `[StyleProfileUtils][handleStyleProfileSetup] tid=${messageId} last message was not an interactive response, continuing style profile setup, response -> ${JSON.stringify(
          interactiveResult,
          null,
          2
        )}`
      );

      // If no interactive response or unrecognized response, proceed with normal flow
      // Fetch current profile from the new API
      let userStyleProfileResponse;
      try {
        userStyleProfileResponse =
          await styleProfileClient.get("/style-profile", {
            params: { userId },
            headers: {
              Authorization: "Bearer SYSTEM", // Update with the correct token
            },
          });

        console.log(
          `[StyleProfileUtils][handleStyleProfileSetup] tid=${messageId} Retrieved user style profile:`,
          userStyleProfileResponse?.data
        );
      } catch (error) {
        console.log(
          `[StyleProfileUtils][handleStyleProfileSetup] tid=${messageId} No existing profile found:`,
          error
        );
        // Profile doesn't exist yet, that's okay
      }

      const userStyleProfile = userStyleProfileResponse?.data?.data;

      // Check which profile attribute needs to be filled next
      if (!userStyleProfile?.userGender || userStyleProfile?.userGender === null) {
        return await StyleProfileUtils.askGender(
          userId,
          caseId,
          caseData,
          messageId,
          messagePlatform,
          chatService
        );
      }
      if (!userStyleProfile?.userBodyType || userStyleProfile.userBodyType === null) {
        return await StyleProfileUtils.askBodyType(
          userId,
          caseId,
          userStyleProfile?.userGender,
          messageId,
          messagePlatform,
          chatService
        );
      }
      if (!userStyleProfile?.userUndertone || userStyleProfile.userUndertone === null) {
        return await StyleProfileUtils.askSkinTone(
          userId,
          caseId,
          messageId,
          messagePlatform,
          chatService
        );
      }
      if (!userStyleProfile?.userAge || userStyleProfile.userAge === null) {
        return await StyleProfileUtils.askAgeRange(
          userId,
          caseId,
          messageId,
          messagePlatform,
          chatService
        );
      }
      return {
        statusCode: 200,
        body: JSON.stringify({
          message: "Style profile complete",
          profileComplete: true,
          profileData: userStyleProfile,
        }),
      };
    } catch (error) {
      console.error(
        `[StyleProfileUtils][handleStyleProfileSetup] tid=${messageId} Error:`,
        {
          error,
          caseId,
          userId,
          errorType:
            error instanceof Error ? error.constructor.name : "Unknown",
          errorMessage:
            error instanceof Error ? error.message : "Unknown error",
        }
      );
      throw error;
    }
  }

  public static async updateUserStyleProfile(
    userId: string,
    updates: any,
    messageId: string,
    styleProfileClient: any
  ) {
    console.log(
      `[StyleProfileUtils][updateUserStyleProfile] tid=${messageId} Updating profile for userId: ${userId}`,
      {
        updates,
      }
    );

    try {
      // Check if profile exists
      let profileExists = false;
      try {
        const existingProfileResponse =
          await styleProfileClient.get("/style-profile", {
            params: { userId },
            headers: {
              Authorization: "Bearer SYSTEM", // Update with the correct token
            },
          });
        profileExists = !!existingProfileResponse?.data?.data;

        console.log(
          `[StyleProfileUtils][updateUserStyleProfile] tid=${messageId} Existing profile check:`,
          {
            exists: profileExists,
            data: existingProfileResponse?.data,
          }
        );
      } catch (error) {
        console.log(
          `[StyleProfileUtils][updateUserStyleProfile] tid=${messageId} No existing profile found:`,
          error
        );
      }

      let response;
      if (!profileExists) {
        // Create new profile
        response = await styleProfileClient.post(
          "/style-profile",
          {
            userId,
            ...updates,
          },
          {
            headers: {
              Authorization: "Bearer SYSTEM", // Update with the correct token
            },
          }
        );
        console.log(
          `[StyleProfileUtils][updateUserStyleProfile] tid=${messageId} Created new profile:`,
          response?.data
        );
      } else {
        // Update existing profile
        response = await styleProfileClient.put(
          "/style-profile",
          {
            userId,
            updates,
          },
          {
            headers: {
              Authorization: "Bearer SYSTEM", // Update with the correct token
            },
          }
        );
        console.log(
          `[StyleProfileUtils][updateUserStyleProfile] tid=${messageId} Updated existing profile:`,
          response?.data
        );
      }

      return response?.data;
    } catch (error) {
      console.error(
        `[StyleProfileUtils][updateUserStyleProfile] tid=${messageId} Error updating profile:`,
        {
          error,
          userId,
          updates,
          errorType:
            error instanceof Error ? error.constructor.name : "Unknown",
          errorMessage:
            error instanceof Error ? error.message : "Unknown error",
        }
      );
      throw error;
    }
  }

  private static async getUserName(
    userId: string,
    messageId: string
  ): Promise<string> {
    try {
      const userProfileDao = new UserInfoDao();
      const userProfile = await userProfileDao.getUserInfoById(userId);

      console.log(
        `[StyleProfileUtils][getUserName] tid=${messageId} Retrieved user profile:`,
        {
          userId,
          hasProfile: !!userProfile?.data,
          name: userProfile?.data?.userFullName,
        }
      );

      return userProfile?.data?.userFullName || "there";
    } catch (error) {
      console.error(
        `[StyleProfileUtils][getUserName] tid=${messageId} Error getting user name:`,
        {
          error,
          userId,
          errorType:
            error instanceof Error ? error.constructor.name : "Unknown",
          errorMessage:
            error instanceof Error ? error.message : "Unknown error",
        }
      );
      return "there"; // Fallback to generic greeting
    }
  }

  private static async askGender(
    userId: string,
    caseId: string,
    caseData: any,
    messageId: string,
    messagePlatform: MessagePlatform,
    chatService: ChatService
  ) {
    console.log(
      `[StyleProfileUtils][askGender] tid=${messageId} Preparing gender question:`,
      { userId, caseData }
    );

    const userName = await StyleProfileUtils.getUserName(userId, messageId);

    const message = {
      messageDirection: MessageDirection.OUTBOUND,
      messagePlatform: messagePlatform,
      messageOrigin: MessageOrigin.SYSTEM,
      userId: userId,
      caseId,
      recipientNumber: userId,
      messageType: "INTERACTIVE_REPLY",
      content: {
        body: {
          text: `Sure ${userName}! To ${ 
            caseData?.useCase === "REVIEW_OUTFIT"
              ? "review the"
              : "curate a personalised"
          } outfit for you, I'll be needing a few inputs.\n\nPlease choose a style closest to your identity:`,
        },
        action: {
          buttons: [
            {
              type: "reply",
              reply: {
                id: "MASCULINE",
                title: "Masculine",
              },
            },
            {
              type: "reply",
              reply: {
                id: "FEMININE",
                title: "Feminine",
              },
            },
          ],
        },
      },
    };

    try {
      console.log(
        `[StyleProfileUtils][askGender] tid=${messageId} Sending gender question:`,
        {
          userId,
          messageType: message.messageType,
          userName,
        }
      );

      await chatService.sendMessage(message);

      return {
        statusCode: 200,
        body: JSON.stringify({
          message: "Gender question sent",
          nextAttribute: "userGender",
          profileComplete: false,
        }),
      };
    } catch (error) {
      console.error(
        `[StyleProfileUtils][askGender] tid=${messageId} Error sending message:`,
        {
          error,
          userId,
          errorType:
            error instanceof Error ? error.constructor.name : "Unknown",
          errorMessage:
            error instanceof Error ? error.message : "Unknown error",
        }
      );
      throw error;
    }
  }

  private static async askBodyType(
    userId: string,
    caseId: string,
    gender: string,
    messageId: string,
    messagePlatform: MessagePlatform,
    chatService: ChatService
  ) {
    console.log(
      `[StyleProfileUtils][askBodyType] tid=${messageId} Preparing body type question:`,
      { userId, gender }
    );

    // Use a single image URL regardless of gender
    const bodyTypeImageUrl =
      "https://monovaoutfits.blob.core.windows.net/onboarding-images/BODY_TYPES.png";

    // Step 1: Send the image first
    const imageMessage = {
      messageDirection: MessageDirection.OUTBOUND,
      messageOrigin: MessageOrigin.SYSTEM,
      messagePlatform: messagePlatform,
      userId: userId,
      caseId,
      recipientNumber: userId,
      messageType: "IMAGE",
      content: {
        url: bodyTypeImageUrl,
      },
    };

    await chatService.sendMessage(imageMessage);

    // Step 2: Prepare the interactive message with unified body type codes (O, H, A, Y, X)
    const bodyTypeOptions = [
      { id: "O", title: "O Type" },
      { id: "H", title: "H Type" },
      { id: "A", title: "A Type" },
      { id: "Y", title: "Y Type" },
      { id: "X", title: "X Type" },
    ];

    const interactiveMessage = {
      messageDirection: MessageDirection.OUTBOUND,
      messageOrigin: MessageOrigin.SYSTEM,
      messagePlatform: messagePlatform,
      userId: userId,
      caseId,
      recipientNumber: userId,
      messageType: "INTERACTIVE_LIST",
      content: {
        body:
          messagePlatform === MessagePlatform.WHATSAPP
            ? {
                text: "Please choose the body type that matches! Protip: Compare your shoulders, waist and hips. Check the comparison in the chart and pick from the options below!",
              }
            : {
                text: "Please choose the body type that matches!",
                caption:
                  "Protip: Compare your shoulders, waist and hips. Check the comparison in the chart and pick from the options below!",
              },
        action: {
          button: "Choose Body Type",
          sections: [
            {
              rows: bodyTypeOptions,
            },
          ],
        },
      },
    };

    // Step 3: Send the interactive message
    await chatService.sendMessage(interactiveMessage);

    return {
      statusCode: 200,
      body: JSON.stringify({
        message: "Body type question sent",
        nextAttribute: "userBodyType",
        profileComplete: false,
      }),
    };
  }

  private static async askSkinTone(
    userId: string,
    caseId: string,
    messageId: string,
    messagePlatform: MessagePlatform,
    chatService: ChatService
  ) {
    console.log(
      `[StyleProfileUtils][askSkinTone] tid=${messageId} Preparing skin tone question:`,
      { userId }
    );

    const message = {
      messageDirection: MessageDirection.OUTBOUND,
      messagePlatform: messagePlatform,
      MessageOrigin: MessageOrigin.SYSTEM,
      userId,
      caseId,
      recipientNumber: userId,
      messageType: "INTERACTIVE_REPLY",
      content: {
        body:
          messagePlatform === MessagePlatform.WHATSAPP
            ? {
                text: "What is your skin undertone like? \n Protip : Check your vein colour in natural light and pick what matches!",
              }
            : {
                text: "What is your skin undertone like?",
                caption:
                  "Protip : Check your vein colour in natural light and pick what matches!",
              },
        header: {
          type: "image",
          image: {
            link: "https://monovaoutfits.blob.core.windows.net/onboarding-images/skin-tone.png",
          },
        },
        action: {
          buttons: [
            {
              type: "reply",
              reply: {
                id: "COOL",
                title: "Cool",
              },
            },
            {
              type: "reply",
              reply: {
                id: "WARM",
                title: "Warm",
              },
            },
          ],
        },
      },
    };

    try {
      console.log(
        `[StyleProfileUtils][askSkinTone] tid=${messageId} Sending skin tone question:`,
        {
          userId,
          messageType: message.messageType,
        }
      );

      await chatService.sendMessage(message);

      return {
        statusCode: 200,
        body: JSON.stringify({
          message: "Skin tone question sent",
          nextAttribute: "userUndertone",
          profileComplete: false,
        }),
      };
    } catch (error) {
      console.error(
        `[StyleProfileUtils][askSkinTone] tid=${messageId} Error sending message:`,
        {
          error,
          userId,
          errorType:
            error instanceof Error ? error.constructor.name : "Unknown",
          errorMessage:
            error instanceof Error ? error.message : "Unknown error",
        }
      );
      throw error;
    }
  }

  private static async askAgeRange(
    userId: string,
    caseId: string,
    messageId: string,
    messagePlatform: MessagePlatform,
    chatService: ChatService
  ) {
    console.log(
      `[StyleProfileUtils][askAgeRange] tid=${messageId} Preparing age range question:`,
      { userId }
    );

    const message = {
      messageDirection: MessageDirection.OUTBOUND,
      messagePlatform: messagePlatform,
      messageOrigin: MessageOrigin.SYSTEM,
      userId,
      caseId,
      recipientNumber: userId,
      messageType: "INTERACTIVE_LIST",
      content: {
        body: {
          text: "Almost done! Please select your age range to help us tailor the perfect outfit",
        },
        action: {
          button: "Your Age Range",
          sections: [
            {
              rows: [
                { id: "AGE_0_20", title: "< 20 years" },
                { id: "AGE_21_24", title: "21-24 years" },
                { id: "AGE_25_30", title: "25-30 years" },
                { id: "AGE_31_36", title: "31-36 years" },
                { id: "AGE_36_45", title: "36-45 years" }, // New age group
                { id: "AGE_46_55", title: "46-55 years" }, // New age group
                { id: "AGE_55_99", title: "55+ years" },
              ],
            },
          ],
        },
      },
    };

    try {
      console.log(
        `[StyleProfileUtils][askAgeRange] tid=${messageId} Sending age range question:`,
        {
          userId,
          messageType: message.messageType,
        }
      );

      await chatService.sendMessage(message);

      return {
        statusCode: 200,
        body: JSON.stringify({
          message: "Age range question sent",
          nextAttribute: "userAge",
          profileComplete: false,
        }),
      };
    } catch (error) {
      console.error(
        `[StyleProfileUtils][askAgeRange] tid=${messageId} Error sending message:`,
        {
          error,
          userId,
          errorType:
            error instanceof Error ? error.constructor.name : "Unknown",
          errorMessage:
            error instanceof Error ? error.message : "Unknown error",
        }
      );
      throw error;
    }
  }
}