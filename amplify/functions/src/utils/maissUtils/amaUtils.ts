import { AIService } from "../../ai/AzureAIIntegrationService";
import {
  <PERSON><PERSON>tatus,
  MessageOrigin,
  MessagePlatform,
} from "../../config/constants";
import { MessageFormatter } from "../MessageFormatter";
import { UseCaseUtils } from "./useCaseUtils";
import { ChatService } from "../../services/chatService";

export class AmaUtils {
  private static aiService = new AIService();
  private static chatService = new ChatService();

  // Maximum number of messages allowed in AMA chat
  private static readonly MESSAGE_LIMIT = 30;

  // System prompt for the AMA chat
  private static readonly AMA_SYSTEM_PROMPT = `You are <PERSON><PERSON>, an AI fashion stylist with deep expertise in style, fashion trends, and wardrobe management. Your role is to provide helpful, fashion-focused guidance while maintaining a friendly, conversational tone.

### Expertise Areas:
1. Fashion Trends & Style Evolution
2. Wardrobe Organization & Management
3. Fashion History & Iconic Styles
4. Sustainable Fashion & Ethical Shopping
5. Fashion Rules & Style Guidelines
6. Fabric Care & Maintenance
7. Fashion Industry & Brands
8. Accessories & Styling Tips

### Response Guidelines:
1. Style & Tone:
   - Professional yet approachable
   - Confident but not condescending
   - Engaging and enthusiastic about fashion
   - Use relevant fashion terminology appropriately
   - Include 1-2 emojis when appropriate

2. Content Structure:
   - Keep responses concise (2-3 paragraphs max)
   - Start with a direct answer
   - Follow with brief supporting details
   - End with actionable advice when relevant

3. Knowledge Application:
   - Reference current fashion trends when applicable
   - Incorporate practical, real-world advice
   - Consider sustainability and budget constraints
   - Acknowledge fashion's subjective nature
   - Base recommendations on established style principles

4. Boundaries:
   - Stay focused on fashion and style topics
   - Avoid medical advice or body image issues
   - Maintain inclusivity in recommendations
   - Respect cultural sensitivities
   - Don't promote specific brands unless asked

### Examples of Good Responses:
- Question: "What's trending this season?"
  Response: "This season's key trends include oversized blazers, statement collars, and chunky loafers 🎯. The color palette focuses on earth tones and unexpected pops of neon. For a wearable approach, try incorporating one trend piece into your existing wardrobe basics."

- Question: "How do I make my clothes last longer?"
  Response: "The secret to longevity in fashion is proper care ✨. Always check garment labels, wash delicates separately, and invest in good hangers. Here's a quick tip: let clothes air dry when possible to maintain fabric integrity and prevent shrinkage."

Remember to maintain Monova's brand voice - knowledgeable, helpful, and fashion-forward while keeping responses practical and accessible.`;

  static async handleAmaChat(
    caseData: any,
    userId: string,
    caseId: string,
    messageId: string,
    messagePlatform: MessagePlatform,
    // Formatted Messages
    messages: any[]
  ) {
    try {
      console.log(
        `[AmaUtils][handleAmaChat] tid=${messageId} Checking for Intent Switch:`
      );
      let intentResponse;
      if (messages.length > 1) {
        intentResponse = await UseCaseUtils.handleCaseUsecaseSetup(
          caseId,
          userId,
          messageId,
          messagePlatform,
          true,
          //sending only the last message since we are checking for intent switch
          messages
        );
      }

      if (
        intentResponse?.type === "INTENT_CLASSIFIED" &&
        intentResponse?.intent !== "AMA"
      ) {
        console.log(
          `[AmaUtils][handleAmaChat] tid=${messageId} Intent Switch Detected:`,
          intentResponse
        );
        console.log(
          `[AmaUtils][handleAmaChat] tid=${messageId} Updating AMA Case to Intent:`,
          intentResponse.intent
        );
        return {
          statusCode: 200,
          type: "INTENT_SWITCH",
          body: JSON.stringify({
            message: "Intent switch detected",
            intent: intentResponse.intent,
          }),
        };
      }

      if (messages.length >= this.MESSAGE_LIMIT) {
        // Check if message limit reached
        await this.handleMessageLimitReached(
          userId,
          caseId,
          messageId,
          messagePlatform
        );
        return {
          statusCode: 200,
          type: "LIMIT_REACHED",
          body: JSON.stringify({
            message: "Message limit reached",
            caseStatus: CaseStatus.CLOSED,
          }),
        };
      }

      // If this was the second-to-last message allowed, send a warning
      if (messages.length === this.MESSAGE_LIMIT - 10) {
        await this.sendMessageLimitWarning(
          userId,
          caseId,
          messageId,
          messagePlatform
        );
      }

      const formattedMessages = MessageFormatter.formatMessages(messages);
      console.log(
        `[AmaUtils][handleAmaChat] tid=${messageId} Formatted Messages:`,
        formattedMessages
      );
      const userPrompts = [];

      // Add new user message
      userPrompts.push(
        JSON.stringify({
          newUserMessage: formattedMessages[0] || "",
        })
      );

      // Add chat history only if previous messages exist
      if (messages.length > 1) {
        userPrompts.push(
          JSON.stringify({
            chatHistory: formattedMessages.slice(1),
          })
        );
      }

      // Get AI response
      const aiResponse = await this.aiService.getAIResponse(
        userPrompts,
        this.AMA_SYSTEM_PROMPT,
        "gpt-4o-mini-2",
        messageId
      );

      // Send response to user
      await this.chatService.sendMessage({
        recipientNumber: userId,
        messageType: "TEXT",
        messageId: messageId,
        caseId: caseId,
        messageDirection: "OUTBOUND",
        messagePlatform: messagePlatform,
        messageOrigin: MessageOrigin.MODEL,
        content: {
          text: aiResponse,
        },
      });

      return {
        statusCode: 200,
        type: "MESSAGE_PROCESSED",
        body: JSON.stringify({
          message: "Message processed successfully",
          messageCount: messages.length,
          caseStatus: CaseStatus.USECASE_IDENTIFIED,
        }),
      };
    } catch (error) {
      console.error(`[AmaUtils][handleAmaChat] tid=${messageId} Error:`, {
        error,
        caseId,
        userId,
      });
      throw error;
    }
  }

  private static async handleMessageLimitReached(
    userId: string,
    caseId: string,
    messageId: string,
    messagePlatform: MessagePlatform
  ) {
    try {
      // Send limit reached message
      await this.chatService.sendMessage({
        recipientNumber: userId,
        messageType: "TEXT",
        messageId: messageId,
        caseId: caseId,
        messageDirection: "OUTBOUND",
        messagePlatform: messagePlatform,
        messageOrigin: MessageOrigin.SYSTEM,
        content: {
          text: "You've reached the message limit for this chat. Please start a new chat to continue our conversation! 🔄✨",
        },
      });
    } catch (error) {
      console.error(
        `[AmaUtils][handleMessageLimitReached] tid=${messageId} Error:`,
        {
          error,
          userId,
          caseId,
        }
      );
      throw error;
    }
  }

  private static async sendMessageLimitWarning(
    userId: string,
    caseId: string,
    messageId: string,
    messagePlatform: MessagePlatform
  ) {
    try {
      await this.chatService.sendMessage({
        recipientNumber: userId,
        messageType: "TEXT",
        messageId: messageId,
        caseId: caseId,
        messageDirection: "OUTBOUND",
        messagePlatform: messagePlatform,
        messageOrigin: MessageOrigin.SYSTEM,
        content: {
          text: "This seems to be becoming a longer chat! For the best experience, consider starting a new chat soon - this helps keep our conversations fresh and organized. ✨",
        },
      });
    } catch (error) {
      console.error(
        `[AmaUtils][sendMessageLimitWarning] tid=${messageId} Error:`,
        {
          error,
          userId,
          caseId,
        }
      );
      throw error;
    }
  }
}
