import { AIService } from "../../ai/AzureAIIntegrationService";
import { TypeChatService } from "../../ai/TypeChatService";
import { MessageFormatter } from "../MessageFormatter";
import { CaseService } from "../../services/caseService";
import {
  MessagePlatform,
  MessageDirection,
  MessageOrigin,
  UseCase,
} from "../../config/constants";
import { ChatService } from "../../services/chatService";

interface ParameterDefinition {
  type: "enum" | "string";
  values?: string[];
  description?: string;
}

interface ParameterDefinitions {
  [key: string]: ParameterDefinition;
}

export class CaseContextUtils {
  private aiService: AIService;
  private caseService: CaseService;
  private typeChatService: TypeChatService;
  private chatService: ChatService;

  constructor(
    aiService: AIService,
    caseService: CaseService,
    typeChatService: TypeChatService,
    chatService: ChatService
  ) {
    this.aiService = aiService;
    this.caseService = caseService;
    this.typeChatService = typeChatService;
    this.chatService = chatService;
  }

  private readonly PARAMETER_DEFINITIONS: ParameterDefinitions = {
    eventType: {
      type: "string",
      description:
        "The type or occasion of the event (e.g., wedding, dinner, meeting)",
    },
    eventTime: {
      type: "enum",
      values: ["EARLY MORNING", "MORNING", "AFTERNOON", "EVENING", "NIGHT"],
      description: "Time of day when the event takes place",
    },
    eventOutfitVibe: {
      type: "enum",
      values:
        [
          "BOLD",
          "PLAYFUL",
          "CASUAL",
          "EDGY",
          "MINIMAL",
          "ETHNIC",
          "BOHEMIAN",
          "SPORTY",
          "ELEGANT",
          "PROFESSIONAL",
          "SURPRISE_ME",
        ],
      description: "The desired style or vibe for the outfit",
    },
  };

  private async getTopRelevantOptions(
    messages: any[],
    paramType: keyof typeof this.PARAMETER_DEFINITIONS,
    messageId: string,
    caseContext?: any
  ): Promise<string[]> {
    // Check if all case context parameters are empty
    if (
      !caseContext ||
      (!caseContext.eventTime && !caseContext.eventOutfitVibe)
    ) {
      console.log(
        `[CaseContextUtils] Case context is empty, returning default options for ${paramType}`
      );
      console.log(
        `[CaseContextUtils] Default options for ${paramType}:`,
        this.PARAMETER_DEFINITIONS[paramType].values || []
      );
      return this.PARAMETER_DEFINITIONS[paramType].values || [];
    }

    const optionSelectionPrompt = `As an AI fashion assistant, analyze the chat history and context to select the 4 most relevant options for ${paramType}.\n                                  Available options: ${JSON.stringify(
                                    this.PARAMETER_DEFINITIONS[paramType].values
                                  )}\n                                  Chat history: ${JSON.stringify(messages)}\n                                  Case context: ${JSON.stringify(caseContext)}\n\n                                  Return exactly 4 most relevant options from the available options list as a JSON array. Base your selection on:\n                                  1. Explicit mentions or preferences in the chat\n                                  2. Time of day and seasonal context if mentioned\n                                  3. Formality level implied in the conversation\n                                  4. Common patterns for similar situations\n                                  5. Return based on relevance to the current conversation context\n\n                                  Return only the JSON array with 4 options. No explanation needed.`;

    try {
      const aiResponse = await this.aiService.getAIResponse(
        [JSON.stringify({ messages: messages, caseContext })],
        optionSelectionPrompt,
        "gpt-4o-mini-2",
        messageId
      );

      const parsedOptions = JSON.parse(aiResponse);
      console.log(
        `[CaseContextUtils] Selected options for ${paramType}:`,
        parsedOptions
      );

      // Ensure we always return exactly 4 options, falling back to defaults if needed
      if (!Array.isArray(parsedOptions)) {
        return this.PARAMETER_DEFINITIONS[paramType].values?.slice(0, 4) || [];
      }
      // If the Ai Responds with more than 4 options, we will only take the first 4
      if (parsedOptions.length > 4) {
        return parsedOptions.slice(0, 4);
      }

      return parsedOptions;
    } catch (error) {
      console.error(`Error getting relevant options for ${paramType}:`, error);
      return this.PARAMETER_DEFINITIONS[paramType].values?.slice(0, 4) || [];
    }
  }

  private async processInteractiveResponse(
    lastMessage: any,
    userId: string,
    caseId: string,
    caseData: any,
    messageId: string,
    messagePlatform: MessagePlatform,
    messages: any[]
  ) {
    if (!lastMessage) {
      console.log(
        `[CaseContextUtils][processInteractiveResponse] lastMessage recevied is null, lastMessage -> ${lastMessage}`
      );
      return null;
    }

    console.log(
      `[CaseContextUtils][processInteractiveResponse] tid=${messageId} Processing interactive response:`,
      {
        userId,
        messageType: lastMessage?.messageType,
      }
    );

    if (lastMessage?.messageType !== "INTERACTIVE") {
      console.log(
        `[CaseContextUtils][processInteractiveResponse] tid=${messageId} Not an interactive message, skipping`
      );
      return null;
    }
    const messageContent = JSON.parse(lastMessage.messageContent);

    console.log(
      `[CaseContextUtils][processInteractiveResponse] tid=${messageId} lastMessage messageContent -> ${JSON.stringify(
        messageContent,
        null,
        2
      )}`
    );
    const responseType = messageContent.type; // 'button_reply' or 'list_reply' or 'input_reply'
    const response = (() => {
      switch (responseType) {
        case "button_reply":
          return messageContent.button_reply;
        case "list_reply":
          return messageContent.list_reply;
        default:
          return null;
      }
    })();

    console.log(
      `[CaseContextUtils][processInteractiveResponse] tid=${messageId} Interactive response details:`,
      {
        responseType,
        responseId: response?.id,
        responseText: response?.text, // For input_reply
        responseTitle: response?.title, // For button/list reply
      }
    );
    try {
      console.log(
        `[CaseContextUtils][processInteractiveResponse] tid=${messageId} Interactive response processing:`,
        caseData
      );
      const caseContext = caseData?.caseContext;
      console.log(
        `[CaseContextUtils][processInteractiveResponse] tid=${messageId} caseContext:`,
        caseContext
      );

      if (!caseContext) {
        console.log(
          `[CaseContextUtils][processInteractiveResponse] tid=${messageId} caseContext not found, returning null`
        );
        return null;
      }

      if (
        this.PARAMETER_DEFINITIONS.eventTime.values?.includes(response.id) &&
        !caseContext.eventTime
      ) {
        console.log(
          `[CaseContextUtils][processInteractiveResponse] tid=${messageId} eventTime found in response`
        );
        const updatedCaseData = {
          caseContext: {
            ...caseContext,
            eventTime: response.id,
          },
        };

        await this.caseService.updateCase(userId, caseId, updatedCaseData);

        return await this.askForMissingParams(
          userId,
          caseId,
          messageId,
          messagePlatform,
          updatedCaseData.caseContext,
          messages
        );
      } else if (
        this.PARAMETER_DEFINITIONS.eventOutfitVibe.values?.includes(
          response.id
        ) &&
        !caseContext.eventOutfitVibe
      ) {
        console.log(
          `[CaseContextUtils][processInteractiveResponse] tid=${messageId} eventOutfitVibe found in response`
        );
        const updatedCaseData = {
          caseContext: {
            ...caseContext,
            eventOutfitVibe: response.id,
          },
        };

        await this.caseService.updateCase(userId, caseId, updatedCaseData);

        return await this.askForMissingParams(
          userId,
          caseId,
          messageId,
          messagePlatform,
          updatedCaseData.caseContext,
          messages
        );
      } else {
        return null;
      }
    } catch (error) {
      console.error(
        `[CaseContextUtils][processInteractiveResponse] tid=${messageId} Error processing interactive response:`,
        {
          error,
          userId,
          responseId: response.id,
          errorType:
            error instanceof Error ? error.constructor.name : "Unknown",
          errorMessage:
            error instanceof Error ? error.message : "Unknown error",
        }
      );
      throw error;
    }
  }

  private async askForMissingParams(
    userId: string,
    caseId: string,
    messageId: string,
    messagePlatform: MessagePlatform,
    caseContext: any,
    messages: any[]
  ) {
    console.log(
      `[CaseContextUtils][askForMissingParams] tid=${messageId} Asking for missing params:`,
      caseContext
    );
    if (!caseContext.eventType) {
      console.log(
        `[CaseContextUtils][askForMissingParams] tid=${messageId} eventType is missing, asking for eventType`
      );
      return await this.askForEventType(
        userId,
        caseId,
        messageId,
        messagePlatform,
        caseContext,
        messages
      );
    } else if (!caseContext.eventTime) {
      console.log(
        `[CaseContextUtils][askForMissingParams] tid=${messageId} eventTime is missing, asking for eventTime`
      );
      return await this.askForEventTime(
        userId,
        caseId,
        messageId,
        messagePlatform,
        caseContext,
        messages
      );
    } else if (!caseContext.eventOutfitVibe && caseContext.useCase !== UseCase.REVIEW_OUTFIT) {
      console.log(
        `[CaseContextUtils][askForMissingParams] tid=${messageId} eventOutfitVibe is missing, asking for eventOutfitVibe`
      );
      return await this.askForEventOutfitVibe(
        userId,
        caseId,
        messageId,
        messagePlatform,
        caseContext,
        messages
      );
    } else {
      console.log(
        `[CaseContextUtils][askForMissingParams] tid=${messageId} No missing params found, caseContext -> ${JSON.stringify(
          caseContext,
          null,
          2
        )}`
      );
      await this.caseService.updateCase(userId, caseId, {
        contextReady: true,
      });
      console.log(
        `[CaseContextUtils][askForMissingParams] tid=${messageId} Updated case contextReady to true`
      );
      return {
        statusCode: 200,
        body: JSON.stringify({
          message: "No missing params",
          caseContext: caseContext,
          contextReady: true,
        }),
      };
    }
  }

  private async askForEventType(
    userId: string,
    caseId: string,
    messageId: string,
    messagePlatform: MessagePlatform,
    caseContext: any,
    messages: any[]
  ) {
    const message = {
      messageDirection: MessageDirection.OUTBOUND,
      messagePlatform: messagePlatform,
      messageOrigin: MessageOrigin.SYSTEM,
      userId,
      caseId,
      recipientNumber: userId,
      messageType: "TEXT",
      content: {
        text: "Describe by typing the occasion in 3 words or fewer in a single sentence! 🙌",
      },
    };

    try {
      console.log(
        `[CaseContextUtils][askForMissingParams] tid=${messageId} Sending message to ask for missing params:`,
        message
      );
      await this.chatService.sendMessage(message);
      return {
        statusCode: 200,
        body: JSON.stringify({
          message: "Asking for missing params",
          caseContext: caseContext,
        }),
      };
    } catch (error) {
      console.error(
        `[CaseContextUtils][askForMissingParams] tid=${messageId} Error asking for missing params:`,
        {
          error,
          userId,
        }
      );
      return {
        statusCode: 500,
        body: JSON.stringify({
          message: "Error asking for missing params",
          caseContext: caseContext,
        }),
      };
    }
  }

  private async askForEventTime(
    userId: string,
    caseId: string,
    messageId: string,
    messagePlatform: MessagePlatform,
    caseContext: any,
    messages: any[]
  ) {
    const relevantOptions = await this.getTopRelevantOptions(
      messages,
      "eventTime",
      messageId,
      caseContext
    );
    console.log(
      `[CaseContextUtils][askForMissingParams] tid=${messageId} relevantOptions for eventTime:`,
      relevantOptions
    );

    const parsedRelevantOptions =
      await this.typeChatService.parseOptionsResponse(
        JSON.stringify(relevantOptions)
      );

    console.log(
      `[CaseContextUtils][askForMissingParams] tid=${messageId} parsedRelevantOptions for eventType:`,
      parsedRelevantOptions
    );

    console.log(
      `[CaseContextUtils][askForMissingParams] tid=${messageId} eventTime is missing, asking for eventTime`
    );
    const message = {
      messageDirection: MessageDirection.OUTBOUND,
      messagePlatform: messagePlatform,
      messageOrigin: MessageOrigin.SYSTEM,
      userId,
      caseId,
      recipientNumber: userId,
      messageType: "INTERACTIVE_REPLY",
      content: {
        body: {
          text: Buffer.from("What time of day is the event? ⌚").toString(
            "utf-8"
          ),
        },
        action: {
          buttons: parsedRelevantOptions.map((eventTime) => ({
            type: "reply",
            reply: {
              id: eventTime,
              title: eventTime.charAt(0) + eventTime.slice(1).toLowerCase(),
            },
          })),
        },
      },
    };
    try {
      console.log(
        `[CaseContextUtils][askForMissingParams] tid=${messageId} Sending message to ask for missing params:`,
        message
      );
      await this.chatService.sendMessage(message);
      return {
        statusCode: 200,
        body: JSON.stringify({
          message: "Asking for missing params",
          caseContext: caseContext,
        }),
      };
    } catch (error) {
      console.error(
        `[CaseContextUtils][askForMissingParams] tid=${messageId} Error asking for missing params:`,
        {
          error,
          userId,
        }
      );
      return {
        statusCode: 500,
        body: JSON.stringify({
          message: "Error asking for missing params",
          caseContext: caseContext,
        }),
      };
    }
  }

  private async askForEventOutfitVibe(
    userId: string,
    caseId: string,
    messageId: string,
    messagePlatform: MessagePlatform,
    caseContext: any,
    messages: any[]
  ) {
    const relevantOptions = await this.getTopRelevantOptions(
      messages,
      "eventOutfitVibe",
      messageId,
      caseContext
    );
    console.log(
      `[CaseContextUtils][askForMissingParams] tid=${messageId} relevantOptions for eventOutfitVibe:`,
      relevantOptions
    );

    const parsedRelevantOptions =
      await this.typeChatService.parseOptionsResponse(
        JSON.stringify(relevantOptions)
      );

    console.log(
      `[CaseContextUtils][askForMissingParams] tid=${messageId} parsedRelevantOptions for eventType:`,
      parsedRelevantOptions
    );

    console.log(
      `[CaseContextUtils][askForMissingParams] tid=${messageId} eventOutfitVibe is missing, asking for eventOutfitVibe`
    );
    const message = {
      messageDirection: MessageDirection.OUTBOUND,
      messagePlatform: messagePlatform,
      messageOrigin: MessageOrigin.SYSTEM,
      userId,
      caseId,
      recipientNumber: userId,
      messageType: "INTERACTIVE_REPLY",
      content: {
        body: {
          text: Buffer.from(
            "What kind of outfit vibe are you going for? 🎨"
          ).toString("utf-8"),
        },
        action: {
          buttons: parsedRelevantOptions.map((eventOutfitVibe) => ({
            type: "reply",
            reply: {
              id: eventOutfitVibe,
              title:
                eventOutfitVibe.charAt(0) +
                eventOutfitVibe.slice(1).toLowerCase(),
            },
          })),
        },
      },
    };
    try {
      console.log(
        `[CaseContextUtils][askForMissingParams] tid=${messageId} Sending message to ask for missing params:`,
        message
      );
      await this.chatService.sendMessage(message);
      return {
        statusCode: 200,
        body: JSON.stringify({
          message: "Asking for missing params",
          caseContext: caseContext,
        }),
      };
    } catch (error) {
      console.error(
        `[CaseContextUtils][askForMissingParams] tid=${messageId} Error asking for missing params:`,
        {
          error,
          userId,
        }
      );
      return {
        statusCode: 500,
        body: JSON.stringify({
          message: "Error asking for missing params",
          caseContext: caseContext,
        }),
      };
    }
  }

  public async gatherCaseContext(
    userId: string,
    messages: any[],
    caseId: string,
    messageId: string,
    messagePlatform: MessagePlatform
  ): Promise<any> {
    console.log(
      `[CaseContextUtils][gatherCaseContext] tid=${messageId} Full messages received:\n`,
      JSON.stringify(messages, null, 2)
    );

    try {
      const caseData = await this.caseService.getCaseById(userId, caseId);
      console.log(
        `[CaseContextUtils][gatherCaseContext] tid=${messageId} caseData:`,
        caseData
      );
      // Get the last message
      const lastMessage = messages[messages.length - 1];
      const formattedMessages = MessageFormatter.formatMessages(messages);
      const interactiveResponse = await this.processInteractiveResponse(
        lastMessage,
        userId,
        caseId,
        caseData,
        messageId,
        messagePlatform,
        formattedMessages
      );

      if (interactiveResponse) {
        return interactiveResponse;
      }

      console.log(
        `[CaseContextUtils][gatherCaseContext] tid=${messageId} last message was not an interactive response, continuing style profile setup, response -> ${JSON.stringify(
          interactiveResponse,
          null,
          2
        )}`
      );

      const caseContext = caseData?.caseContext;
      const useCase = caseData?.useCase;
      console.log(
        `[CaseContextUtils][gatherCaseContext] tid=${messageId} caseContext:`,
        caseContext
      );

      const CaseContextGatheringPrompt = `You are an AI assistant specialized in analyzing conversation history to extract and classify specific parameters about events and preferences. Your task is to identify and map relevant information from the chat history to predefined parameter categories.\n\n### Input Format\nYou will receive:\n- chatHistory: An array of conversation messages between the user and assistant\n- parameterDefinitions: A dictionary defining the expected parameters and their allowed values\n\n### Parameter Categories and Valid Values\nEvent Parameters:\n- eventType: ${JSON.stringify(this.PARAMETER_DEFINITIONS.eventType.description)} \n- eventTime: ${JSON.stringify(
        this.PARAMETER_DEFINITIONS.eventTime
          .values
      )} | null\n${
        useCase !== UseCase.REVIEW_OUTFIT
          ? `- eventOutfitVibe: ${JSON.stringify(
              this.PARAMETER_DEFINITIONS
                .eventOutfitVibe.values
            )} | null`
          : ""
      }\n\n### Task\n1. Analyze the chat history to identify any mentions or implications of the defined parameters\n2. For eventType, capture the exact event description as mentioned in the conversation\n3. Map the identified information to the closest matching predefined value from the parameter definitions\n3. Only classify information that is explicitly stated or can be confidently inferred\n4. Do not fill parameters if the information is ambiguous or unclear\n\n### Output Format\nReturn a JSON object with:\njson\n{\n  "inferredParameters": {\n    "parameterName": "ENUM_VALUE",\n    ...\n  }\n}\n\n### Important Notes\n- Only include parameters that can be confidently inferred from the conversation\n- Map values to the closest matching predefined enum value\n- Do not make assumptions about parameters that aren't clearly indicated\n- If a parameter value doesn't clearly map to a predefined option, omit it from the output\n- Maintain consistency with the enum values provided in the parameter definitions\n\nReturn only the JSON output. Do not include any additional text or explanations.`;

      // Call ai and fill in case context for params provided in the message
      let parsedResponse;
      if (useCase === UseCase.REVIEW_OUTFIT) {
        const userPrompt = {
          chatHistory: formattedMessages,
          parameterDefinitions: {
            eventType: this.PARAMETER_DEFINITIONS.eventType,
            eventTime: this.PARAMETER_DEFINITIONS.eventTime,
          },
        };

        console.log(
          `[CaseContextUtils][gatherCaseContext] tid=${messageId} AI input:`,
          JSON.stringify(userPrompt, null, 2)
        );

        const aiResponse = await this.aiService.getImageAnalysis(
          caseContext.userOutfitImage,
          JSON.stringify(userPrompt),
          CaseContextGatheringPrompt,
          "gpt-4o-mini-2"
        );

        console.log(
          `[CaseContextUtils][gatherCaseContext] tid=${messageId} AI response:`,
          aiResponse
        );

        parsedResponse = await this.typeChatService.parseReviewContextResponse(
          JSON.stringify(aiResponse)
        );
      } else if (useCase === UseCase.OUTFIT_CURATION) {
        const userPrompt = {
          chatHistory: formattedMessages,
          parameterDefinitions: {
            eventType: this.PARAMETER_DEFINITIONS.eventType,
            eventTime: this.PARAMETER_DEFINITIONS.eventTime,
            eventOutfitVibe: this.PARAMETER_DEFINITIONS.eventOutfitVibe,
          },
        };
        console.log(
          `[CaseContextUtils][gatherCaseContext] tid=${messageId} AI input:`,
          JSON.stringify(userPrompt, null, 2)
        );
        const aiResponse = await this.aiService.getAIResponse(
          [JSON.stringify(userPrompt)],
          CaseContextGatheringPrompt,
          "gpt-4o-mini-2"
        );
        console.log(
          `[CaseContextUtils][gatherCaseContext] tid=${messageId} AI response:`,
          aiResponse
        );
        parsedResponse =
          await this.typeChatService.parseCurationContextResponse(aiResponse);
      }

      console.log(
        `[CaseContextUtils][gatherCaseContext] tid=${messageId} Parsed response:`,
        parsedResponse
      );

      const updatedCaseData = await this.caseService.updateCase(
        userId,
        caseId,
        {
          caseContext: {
            ...caseContext,
            ...parsedResponse,
          },
        }
      );
      console.log(
        `[CaseContextUtils][gatherCaseContext] tid=${messageId} Updated case data after 2s:`,
        updatedCaseData
      );

      return await this.askForMissingParams(
        userId,
        caseId,
        messageId,
        messagePlatform,
        parsedResponse,
        formattedMessages
      );
    } catch (error) {
      console.error(
        `[CaseContextUtils][gatherCaseContext] tid=${messageId} Error gathering case context:`,
        {
          error,
          userId,
        }
      );
    }
  }
}