import { AIService } from "../../ai/AzureAIIntegrationService";
import { MessageOrigin, MessagePlatform } from "../../config/constants";
import { ImageStorageService } from "../../integrations/ImageStorageService";
import { TypeChatService } from "../../ai/TypeChatService";
import { ChatService } from "../../services/chatService";

export class OutfitUtils {
  private static aiService = new AIService();
  private static imageStorageService = new ImageStorageService();
  private static typeChatService = new TypeChatService();
  private static chatService = new ChatService();

  private static async generateWaitingMessage(
    caseContext: any,
    messageId: string
  ): Promise<string> {
    const waitingMessagePrompt = `As a fashion AI assistant, create a brief, friendly waiting message (max 20 words) for a user while their outfit is being generated.
    
Context:
- Event Type: ${caseContext.eventType}
- Time: ${caseContext.eventTime}
- Vibe: ${caseContext.eventOutfitVibe}

Guidelines:
- Be warm and engaging
- Use relevant emojis (2-3 max)
- Mention the specific event/occasion
- Keep it brief but personal
- Create excitement for the upcoming outfit

Response format: Return only the message text with emojis, no explanations or additional formatting.`;

    try {
      const waitingMessage = await this.aiService.getAIResponse(
        [JSON.stringify({ caseContext })],
        waitingMessagePrompt,
        "gpt-4o-mini-2",
        messageId
      );

      return waitingMessage.trim();
    } catch (error) {
      // Fallback message in case of error
      return "Creating your perfect outfit, just a moment! ✨👗";
    }
  }

  static async generateOutfit(
    caseData: any,
    userId: string,
    caseId: string,
    messageId: string,
    messagePlatform: MessagePlatform
  ) {
    const outfitCurationPrompt =
      "# Role: You are an exceptional AI Stylist. You possess in-depth knowledge and skills in fashion, styling, and wardrobe management. Objective: Your main objective is to provide personalized styling advice and wardrobe management tips. These questions will be about Monova, a platform revolutionizing personal styling and wardrobe management with AI-powered tools. To achieve this, you must adhere to the following steps: Step 1: Outfit suggestion should follow this format taking into consideration the different parameters given as input and every. Make sure that the sentences are concise enough to give the minimum required information. For every apparel, we need to define the basic info that helps the user judge. It can be colour, fit, style etc. Make sure each category is well within 10 words and should be in the same line after every category as it will be then passed on as JSON for image generation. Top wear, Bottom wear, Accessories, Shoes. Audience: You will interact with fashion-conscious individuals and busy professionals residing in tier-1 cities of India. These users are primarily interested in optimizing their wardrobe, receiving personalized outfit suggestions, and exploring curated shopping options. Style: Your communication style should be professional, yet engaging.Do not use text formatting like bold or italics. No new line should be there. The response has to be in a single line. Other Rules: If a user asks questions beyond the scope of fashion, styling, or wardrobe management, do not address these queries directly. Instead, guide them back to the topics you can assist with by providing a list of relevant subjects or resources.";

    try {
      // Generate outfit description
      console.log(
        `[OutfitUtils][generateOutfit] tid=${messageId} Generating outfit for user ${userId}`
      );
      const outfitDesc = await this.aiService.getAIResponse(
        [
          JSON.stringify({
            userProfile: caseData.userProfile,
            caseContext: caseData.caseContext,
          }),
        ],
        outfitCurationPrompt,
        "gpt-4o-mini-2"
      );

      console.log(
        `[OutfitUtils][generateOutfit] tid=${messageId} Outfit generated for user ${userId}`,
        outfitDesc
      );

      const parsedOutfitDesc = await this.typeChatService.parseOutfitResponse(
        JSON.stringify(outfitDesc)
      );

      console.log(
        `[OutfitUtils][generateOutfit] tid=${messageId} Parsed Outfit Description`,
        parsedOutfitDesc
      );

      // //! Add type chat for this to be able to send this message coz this may be a tring not an object

      const formattedOutfitDesc = Object.entries(parsedOutfitDesc)
        .map(([category, details]) => `**${category}**: ${details}`)
        .join("\n");

      console.log(
        `[OutfitUtils][generateOutfit] tid=${messageId} Formatted Outfit:`,
        formattedOutfitDesc
      );

      await this.chatService.sendMessage({
        recipientNumber: userId,
        messageType: "TEXT",
        messageId: messageId,
        caseId: caseId,
        messageDirection: "OUTBOUND",
        messagePlatform: messagePlatform,
        messageOrigin: MessageOrigin.MODEL,
        content: {
          text: formattedOutfitDesc,
        },
      });

      console.log(
        `[OutfitUtils][generateOutfit] tid=${messageId} Sending outfit to user ${userId}`
      );

      console.log(
        `[OutfitUtils][generateOutfit] tid=${messageId} Sending Wait Message to user ${userId}`
      );

      // Send waiting message asynchronously

      this.generateWaitingMessage(caseData.caseContext, messageId)
        .then((waitingMessage) => {
          this.chatService
            .sendMessage({
              recipientNumber: userId,
              messageType: "TEXT",
              messageId: messageId,
              caseId: caseId,
              messageDirection: "OUTBOUND",
              messagePlatform: messagePlatform,
              messageOrigin: MessageOrigin.MODEL,
              content: {
                text: Buffer.from(waitingMessage).toString("utf-8"),
              },
            })
            .catch((error) =>
              console.error(
                `[OutfitUtils][generateOutfit] Failed to send waiting message: ${error}`
              )
            );
        })
        .catch((error) =>
          console.error(
            `[OutfitUtils][generateOutfit] Failed to generate waiting message: ${error}`
          )
        );

      console.log(
        `[OutfitUtils][generateOutfit] tid=${messageId} Creating  Outfit for user ${userId}`,
        outfitDesc
      );
      // Generate image based on description
      const outfitGenerationPrompt = `Create a flatlay square image featuring an outfit with the following items: ${outfitDesc} Arrange each item separately in a grid on a plain, neutral background. Ensure the items are clearly visible, well-spaced, and proportional in size. Avoid overcrowding.`;
      const imageUrlByAI = await this.aiService.generateImage(
        outfitGenerationPrompt
      );

      console.log(
        `[OutfitUtils][generateOutfit] tid=${messageId} Sending Image to user ${userId}`,
        imageUrlByAI
      );

      console.log(
        `[OutfitUtils][generateOutfit] tid=${messageId} Saving Image to Azure`
      );

      //! NOTE : IAMGE PATH GENNERATEED BY AZURE IS PUBLIC FOR NOW BUT IF WE MAKE IT PREVIATE WE WILL HAVE TO USE SAS TOKENS
      //! FOR WHICH THE FUNCTIONLAITY IS ALREADY PRESENT IN THE IMAGE STORAGE SERVICE
      const imagePath = await this.imageStorageService.uploadImage(
        imageUrlByAI,
        caseId
      );

      console.log(
        `[OutfitUtils][generateOutfit] tid=${messageId} Image saved`,
        imagePath
      );

      await this.chatService.sendMessage({
        recipientNumber: userId,
        messageType: "IMAGE",
        messageId: messageId,
        caseId: caseId,
        messageDirection: "OUTBOUND",
        messagePlatform: messagePlatform,
        messageOrigin: MessageOrigin.MODEL,
        content: {
          url: imagePath,
          caption: "Here's your personalized outfit visualization!",
        },
      });

      console.log(
        `[OutfitUtils][generateOutfit] tid=${messageId} Updating Case Status to Closed for user ${userId}`
      );

      return {
        statusCode: 200,
        body: JSON.stringify({
          message: "Outfit generated successfully",
          outfitDesc,
          imageUrlByAI,
        }),
      };
    } catch (error) {
      console.error(`[OutfitUtils][generateOutfit] tid=${messageId} Error:`, {
        error,
        caseId,
        userId,
      });
      throw error;
    }
  }
}
