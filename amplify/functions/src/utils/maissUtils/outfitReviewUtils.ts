import { AIService } from "../../ai/AzureAIIntegrationService";
import { MessageOrigin, MessagePlatform } from "../../config/constants";
import { ChatService } from "../../services/chatService";

export class OutfitReviewUtils {
  private static aiService = new AIService();
  private static chatService = new ChatService();

  static async reviewOutfit(
    caseData: any,
    userId: string,
    caseId: string,
    messageId: string,
    messagePlatform: MessagePlatform,
    imageUrl: string
  ) {
    console.log('[OutfitReviewUtils][reviewOutfit] Starting outfit review', {
      messageId,
      caseId,
      userId,
      messagePlatform
    });

    const reviewPrompt = `Please analyze the following outfit and provide actionable feedback.
  
  User Details and Image will be shared by the user
  
  Desired Feedback:
  1. Compliment: Start with a positive comment about the outfit.
  2. Outfit Suggestions: Provide specific suggestions to improve the outfit. Share a quick hack which they can implement if they feel they are short on time and also a version that really improves their outfit if they have time to do so
  3. Color Coordination: Suggest colors that would complement the user's skin undertone and the occasion.
  4. Accessory Recommendations: Recommend suitable accessories to enhance the outfit.
  5. Styling Tips: Provide any additional styling tips relevant to the occasion.
  End with a beautiful quote
  Use emojis to make the chat friendly and warm
  Keep it conversational, so don't really use headings. Do not mention their body type, skin tone etc in your reply as the user might feel mocked when mentioned their body type and skin undertone.
  
  Please ensure the feedback is concise, within 75 words maximum and easy to understand, suitable for a short text message format.`;

    try {
      console.log('[OutfitReviewUtils][reviewOutfit] Calling AI service for image analysis', {
        messageId,
        imageUrl
      });

      const reviewResponse = await this.aiService.getImageAnalysis(
        imageUrl,
        JSON.stringify({
          userProfile: caseData.userProfile,
          caseContext: caseData.caseContext,
        }),
        reviewPrompt
      );

      console.log('[OutfitReviewUtils][reviewOutfit] AI analysis completed', {
        messageId,
        responseLength: reviewResponse?.length
      });
      
      console.log('[OutfitReviewUtils][reviewOutfit] Sending chat message', {
        messageId,
        userId,
        caseId
      });

      await this.chatService.sendMessage({
        recipientNumber: userId,
        messageType: "TEXT",
        messageId: messageId,
        caseId: caseId,
        messageDirection: "OUTBOUND",
        messagePlatform: messagePlatform,
        messageOrigin: MessageOrigin.MODEL,
        content: {
          text: Buffer.from(reviewResponse).toString("utf-8"),
        },
      });

      console.log('[OutfitReviewUtils][reviewOutfit] Chat message sent successfully', {
        messageId
      });

      return {
        statusCode: 200,
        body: JSON.stringify({
          message: "Outfit review completed successfully",
          review: reviewResponse,
        }),
      };
    } catch (error) {
      console.error(
        `[OutfitReviewUtils][reviewOutfit] Error processing outfit review tid=${messageId}`,
        {
          error,
          caseId,
          userId,
        }
      );
      throw error;
    }
  }
}
