import { AIService } from "../../ai/AzureAIIntegrationService";
import {
  CaseStatus,
  MessageOrigin,
  MessagePlatform,
} from "../../config/constants";

import { TypeChatService } from "../../ai/TypeChatService";
import { IntentResponse } from "../../types/schema";
import { MessageFormatter } from "../MessageFormatter";

import { ChatService } from "../../services/chatService";
import { CaseService } from "../../services/caseService";


// interface AIResponse {
//   classifiedIntent: (typeof CaseStatus)[keyof typeof CaseStatus] | null;
//   followUpMessage: string | null;
// }

export interface UseCaseResponse {
  type: "INTENT_CLASSIFIED" | "INTENT_REQUIRED";
  message: string;
  options: Array<(typeof CaseStatus)[keyof typeof CaseStatus]>;
  intent?: string;
  imageUrl?: string;
}

export class UseCaseUtils {
  private static aiService = new AIService();
  private static typeChatService = new TypeChatService();
  private static caseService = new CaseService(UseCaseUtils.aiService);
  private static chatService = new ChatService(UseCaseUtils.caseService);

  private static extractImageUrl(latestMessage: any): string | undefined {
    console.log(
      `[UseCaseUtils][extractImageUrl] Extracting image URL from messages:`,
      latestMessage
    );
    if (!latestMessage) return undefined;
    const parsedMessageContent = JSON.parse(latestMessage.messageContent);

    if (parsedMessageContent?.url) {
      return parsedMessageContent.url;
    }
    return undefined;
  }

  static async handleCaseUsecaseSetup(
    caseId: string,
    userId: string,
    messageId: string,
    messagePlatform: MessagePlatform,
    skipFollowUp: boolean,
    messages?: any
  ): Promise<UseCaseResponse> {
    console.log(
      `[UseCaseUtils][handleCaseUsecaseSetup] tid=${messageId} Processing usecase setup:`,
      { caseId, userId, messages }
    );

    try {
      // Prepare the messages for AI analysis

      const IntentIdentificationSystemPrompt = `You are Monova, an AI stylist specializing in fashion advice and outfit recommendations. Your task is to classify user messages into one of the predefined intents or generate a follow-up message to clarify ambiguous inputs. If the intent cannot be determined with certainty, rephrase or expand the user's message to gather more information while maintaining a friendly tone aligned with Monova's brand voice.

### Predefined Intents:
1. **OUTFIT_CURATION**: The user is requesting an outfit suggestion for an occasion or vibe. Examples:
   - "What should I wear for a wedding?"
   - "Can you help me pick an outfit for tonight?"

2. **REVIEW_OUTFIT**: The user is seeking feedback or suggestions on an outfit. Examples:
   - "Does this look good?"
   - "What do you think about this outfit?"

3. **AMA (Ask Me Anything)**: The user is asking general questions unrelated to outfit curation or review. This intent acts as a fallback. Examples:
   - "What's your favorite color?"
   - "Tell me about the latest trends in fashion."

### Output Format:
Respond strictly in this JSON format:
{
  "classifiedIntent": <enum value from predefined intents or null>,
  "followUpMessage": <String or null>
}

### Guidelines:
1. **Intent Classification:**
   - If the user's intent is clear based on the newUserMessage and hatHistory, set classifiedIntent to the appropriate enum value and followUpMessage to null.
   
2. **Follow-Up Message:**
   - If the intent is unclear, set classifiedIntent to null and generate a polite, helpful followUpMessage. The follow-up should:
     - Rephrase or expand the user's message for clarity.
     - Include a specific question to narrow down the intent.
   - Examples:
     - Input: "Can you help me?" → Follow-up: "Are you looking for an outfit suggestion or feedback on an outfit?"
     - Input: "What do you think?" → Follow-up: "Do you want feedback on an outfit or general advice?"

3. **Consider Chat History:**
   - Use chatHistory for context. For instance, if the user previously mentioned an event, ambiguous queries like "What do you think?" should infer context from the prior messages.

4. **Tone and Clarity:**
   - Be polite, professional, and engaging.
   - Ensure the follow-up messages are concise and relevant to the user's query.

### Input Details:
- chatHistory: The list of messages exchanged between the user and Monova so far.
- newUserMessage: The latest user message requiring intent classification.

### Notes:
- Avoid unnecessary follow-ups for clear messages.
- Do not classify prematurely if the intent is ambiguous.
- Always aim to clarify user intent effectively within a single interaction.`;

      console.log(
        `[UseCaseUtils][handleCaseUsecaseSetup] tid=${messageId} Calling AI service with prompts:`,
        {
          systemPrompt: IntentIdentificationSystemPrompt,
          userMessages: messages?.join("\n") || "",
        }
      );

      const formattedMessages = MessageFormatter.formatMessages(messages);
      console.log(
        `[UseCaseUtils][handleCaseUsecaseSetup] tid=${messageId} Formatted messages:`,
        formattedMessages
      );

      const userPrompts = [];

      // Add new user message
      userPrompts.push(
        JSON.stringify({
          newUserMessage: formattedMessages[0] || "",
        })
      );

      // Add chat history only if previous formattedMessages exist
      if (formattedMessages.length > 1) {
        userPrompts.push(
          JSON.stringify({
            chatHistory: formattedMessages.slice(1),
          })
        );
      }

      const aiResponse = await this.aiService.getAIResponse(
        userPrompts,
        IntentIdentificationSystemPrompt,
        "gpt-4o-mini-2",
        messageId
      );

      console.log(
        `[UseCaseUtils][handleCaseUsecaseSetup] tid=${messageId} AI response received:`,
        aiResponse
      );

      console.log(
        `[UsecaseUtils][handleCaseUsecaseSetup] tid=${messageId} Type Chat Parsing AI response`
      );
      const parsedResponse: IntentResponse =
        await this.typeChatService.parseIntentResponse(
          JSON.stringify(aiResponse)
        );

      console.log(
        `[UseCaseUtils][handleCaseUsecaseSetup] tid=${messageId} AI response parsed:`,
        parsedResponse
      );

      if (parsedResponse.classifiedIntent) {
        console.log(
          `[UseCaseUtils][handleCaseUsecaseSetup] tid=${messageId} Intent classified:`,
          parsedResponse.classifiedIntent
        );

        let imageUrl: string | undefined;
        if (parsedResponse.classifiedIntent.toUpperCase() === "REVIEW_OUTFIT") {
          imageUrl = this.extractImageUrl(messages[0]);
        }

        console.log(
          `[UseCaseUtils][handleCaseUsecaseSetup] tid=${messageId} Image URL:`,
          imageUrl
        );

        // Update case with the classified intent
        await this.updateCaseIntent(
          caseId,
          userId,
          CaseStatus.USECASE_IDENTIFIED,
          messageId,
          parsedResponse.classifiedIntent,
          imageUrl
        );

        return {
          type: "INTENT_CLASSIFIED",
          message: `Intent classified as ${parsedResponse.classifiedIntent}`,
          options: Object.values(CaseStatus),
          intent: parsedResponse.classifiedIntent.toUpperCase(),
          imageUrl,
        };
      } else if (parsedResponse.followUpMessage) {
        console.log(
          `[UseCaseUtils][handleCaseUsecaseSetup] tid=${messageId} Follow-up required:`,
          parsedResponse.followUpMessage
        );

        // Send follow-up question only if it's not skipped by ama
        if (!skipFollowUp) {
          await this.sendMessage(
            userId,
            caseId,
            parsedResponse.followUpMessage,
            messagePlatform,
            messageId
          );
        }

        return {
          type: "INTENT_REQUIRED",
          message: parsedResponse.followUpMessage,
          options: Object.values(CaseStatus),
        };
      }

      throw new Error("Invalid AI response format");
    } catch (error) {
      console.error(
        `[UseCaseUtils][handleCaseUsecaseSetup] tid=${messageId} Error in handleCaseUsecaseSetup:`,
        {
          error,
          caseId,
          userId,
          messagePlatform,
          errorMessage:
            error instanceof Error ? error.message : "Unknown error",
        }
      );
      throw error;
    }
  }

  private static async updateCaseIntent(
    caseId: string,
    userId: string,
    caseStatus: CaseStatus,
    messageId: string,
    intent: string,
    imageUrl?: string
  ) {
    console.log(
      `[UseCaseUtils][updateCaseIntent] tid=${messageId} Updating case intent:`,
      { caseId, userId, intent }
    );
    try {
      const caseContext = {
        userOutfitImage : imageUrl
      }
      await this.caseService.updateCase(userId, caseId, {
        caseStatus: caseStatus,
        caseIntent: intent,
        caseContext: caseContext
      });
      console.log(
        `[UseCaseUtils][updateCaseIntent] tid=${messageId} Case intent updated successfully`
      );
    } catch (error) {
      console.error(
        `[UseCaseUtils][updateCaseIntent] tid=${messageId} Error updating case intent:`,
        {
          error,
          caseId,
          userId,
          intent,
          errorMessage:
            error instanceof Error ? error.message : "Unknown error",
        }
      );
      throw error;
    }
  }

  private static async sendMessage(
    userId: string,
    caseId: string,
    message: string,
    messagePlatform: MessagePlatform,
    messageId: string
  ) {
    console.log(
      `[UseCaseUtils][sendMessage] tid=${messageId} Sending WhatsApp message:`,
      { userId, message }
    );
    try {
      await this.chatService.sendMessage({
        recipientNumber: userId, // WhatsApp number is the userId
        messageType: "TEXT",
        messageDirection: "OUTBOUND",
        messagePlatform: messagePlatform,
        messageOrigin: MessageOrigin.MODEL,
        caseId,
        content: {
          text: message,
        },
      });

      console.log(
        `[UseCaseUtils][sendMessage] tid=${messageId} WhatsApp message sent successfully:`,
        {
          recipientNumber: userId,
          message,
          messagePlatform,
        }
      );
    } catch (error) {
      console.error(
        `[UseCaseUtils][sendMessage] tid=${messageId} Error sending WhatsApp message:`,
        {
          error,
          userId,
          message,
          messagePlatform,
          errorMessage:
            error instanceof Error ? error.message : "Unknown error",
        }
      );
      throw error;
    }
  }
}
