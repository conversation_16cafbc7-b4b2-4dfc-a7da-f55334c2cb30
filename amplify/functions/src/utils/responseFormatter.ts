import { APIGatewayProxyResult } from "aws-lambda";

type Headers = {
  "Access-Control-Allow-Origin": string;
  "Access-Control-Allow-Headers": string;
  "Access-Control-Allow-Credentials"?: string;
  "Access-Control-Allow-Methods"?: string;
  "Set-Cookie"?: string;
}

type ResponseHeaders = Headers & {
  [header: string]: boolean | number | string;
}

interface ErrorResponse {
  message: string;
  code?: string;
  details?: unknown;
}

const DEFAULT_HEADERS: ResponseHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers": "*"
};

const COOKIE_ENABLED_HEADERS: ResponseHeaders = {
  "Access-Control-Allow-Origin": "https://chat.monova.in",
  "Access-Control-Allow-Headers": "Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token,Set-Cookie",
  "Access-Control-Allow-Credentials": "true",
  "Access-Control-Allow-Methods": "OPTIONS,POST,GET"
};

export const formatSuccessResponse = (
  data: unknown,
  message: string = "Success",
  statusCode: number = 200,
  cookieSettings?: string[]
): APIGatewayProxyResult => {
  const headers = cookieSettings 
    ? { ...COOKIE_ENABLED_HEADERS, "Set-Cookie": cookieSettings.join("; ") }
    : DEFAULT_HEADERS;

  return {
    statusCode,
    headers,
    body: JSON.stringify({
      success: true,
      message,
      data
    })
  };
};

export const formatErrorResponse = (
  error: Error | string,
  statusCode: number = 500,
  code?: string
): APIGatewayProxyResult => {
  const errorResponse: ErrorResponse = {
    message: error instanceof Error ? error.message : error
  };

  if (code) {
    errorResponse.code = code;
  }

  // Add stack trace in non-production environments
  if (process.env.NODE_ENV !== 'production' && error instanceof Error) {
    errorResponse.details = error.stack;
  }

  return {
    statusCode,
    headers: DEFAULT_HEADERS,
    body: JSON.stringify({
      success: false,
      error: errorResponse
    })
  };
};

export const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  CONFLICT: 409,
  INTERNAL_SERVER_ERROR: 500
} as const;

export const ERROR_CODES = {
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  AUTHENTICATION_ERROR: 'AUTHENTICATION_ERROR',
  NOT_FOUND: 'NOT_FOUND',
  CONFLICT: 'CONFLICT',
  INTERNAL_ERROR: 'INTERNAL_ERROR'
} as const; 