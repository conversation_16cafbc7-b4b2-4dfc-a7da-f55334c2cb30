/*
* Copyright 2021 Amazon.com, Inc. or its affiliates. All Rights Reserved.
*
* Licensed under the Apache License, Version 2.0 (the "License").
* You may not use this file except in compliance with the License.
* A copy of the License is located at
*
*  http://aws.amazon.com/apache2.0
*
* or in the "license" file accompanying this file. This file is distributed
* on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either
* express or implied. See the License for the specific language governing
* permissions and limitations under the License.
*/

// NOTE: This file is generated and may not follow lint rules defined in your app
// Generated files can be excluded from analysis in analysis_options.yaml
// For more info, see: https://dart.dev/guides/language/analysis-options#excluding-code-from-analysis

// ignore_for_file: public_member_api_docs, annotate_overrides, dead_code, dead_codepublic_member_api_docs, depend_on_referenced_packages, file_names, library_private_types_in_public_api, no_leading_underscores_for_library_prefixes, no_leading_underscores_for_local_identifiers, non_constant_identifier_names, null_check_on_nullable_type_parameter, override_on_non_overriding_member, prefer_adjacent_string_concatenation, prefer_const_constructors, prefer_if_null_operators, prefer_interpolation_to_compose_strings, slash_for_doc_comments, sort_child_properties_last, unnecessary_const, unnecessary_constructor_name, unnecessary_late, unnecessary_new, unnecessary_null_aware_assignments, unnecessary_nullable_for_final_variable_declarations, unnecessary_string_interpolations, use_build_context_synchronously

import 'ModelProvider.dart';
import 'package:amplify_core/amplify_core.dart' as amplify_core;


/** This is an auto generated class representing the UserInfo type in your schema. */
class UserInfo extends amplify_core.Model {
  static const classType = const _UserInfoModelType();
  final String? _userId;
  final String? _userFullName;
  final String? _userWhatsAppNumber;
  final UserInfoUserStatus? _userStatus;
  final amplify_core.TemporalDateTime? _createdAt;
  final amplify_core.TemporalDateTime? _updatedAt;

  @override
  getInstanceType() => classType;
  
  @Deprecated('[getId] is being deprecated in favor of custom primary key feature. Use getter [modelIdentifier] to get model identifier.')
  @override
  String getId() => modelIdentifier.serializeAsString();
  
  UserInfoModelIdentifier get modelIdentifier {
    try {
      return UserInfoModelIdentifier(
        userId: _userId!
      );
    } catch(e) {
      throw amplify_core.AmplifyCodeGenModelException(
          amplify_core.AmplifyExceptionMessages.codeGenRequiredFieldForceCastExceptionMessage,
          recoverySuggestion:
            amplify_core.AmplifyExceptionMessages.codeGenRequiredFieldForceCastRecoverySuggestion,
          underlyingException: e.toString()
          );
    }
  }
  
  String get userId {
    try {
      return _userId!;
    } catch(e) {
      throw amplify_core.AmplifyCodeGenModelException(
          amplify_core.AmplifyExceptionMessages.codeGenRequiredFieldForceCastExceptionMessage,
          recoverySuggestion:
            amplify_core.AmplifyExceptionMessages.codeGenRequiredFieldForceCastRecoverySuggestion,
          underlyingException: e.toString()
          );
    }
  }
  
  String get userFullName {
    try {
      return _userFullName!;
    } catch(e) {
      throw amplify_core.AmplifyCodeGenModelException(
          amplify_core.AmplifyExceptionMessages.codeGenRequiredFieldForceCastExceptionMessage,
          recoverySuggestion:
            amplify_core.AmplifyExceptionMessages.codeGenRequiredFieldForceCastRecoverySuggestion,
          underlyingException: e.toString()
          );
    }
  }
  
  String get userWhatsAppNumber {
    try {
      return _userWhatsAppNumber!;
    } catch(e) {
      throw amplify_core.AmplifyCodeGenModelException(
          amplify_core.AmplifyExceptionMessages.codeGenRequiredFieldForceCastExceptionMessage,
          recoverySuggestion:
            amplify_core.AmplifyExceptionMessages.codeGenRequiredFieldForceCastRecoverySuggestion,
          underlyingException: e.toString()
          );
    }
  }
  
  UserInfoUserStatus? get userStatus {
    return _userStatus;
  }
  
  amplify_core.TemporalDateTime? get createdAt {
    return _createdAt;
  }
  
  amplify_core.TemporalDateTime? get updatedAt {
    return _updatedAt;
  }
  
  const UserInfo._internal({required userId, required userFullName, required userWhatsAppNumber, userStatus, createdAt, updatedAt}): _userId = userId, _userFullName = userFullName, _userWhatsAppNumber = userWhatsAppNumber, _userStatus = userStatus, _createdAt = createdAt, _updatedAt = updatedAt;
  
  factory UserInfo({required String userId, required String userFullName, required String userWhatsAppNumber, UserInfoUserStatus? userStatus}) {
    return UserInfo._internal(
      userId: userId,
      userFullName: userFullName,
      userWhatsAppNumber: userWhatsAppNumber,
      userStatus: userStatus);
  }
  
  bool equals(Object other) {
    return this == other;
  }
  
  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is UserInfo &&
      _userId == other._userId &&
      _userFullName == other._userFullName &&
      _userWhatsAppNumber == other._userWhatsAppNumber &&
      _userStatus == other._userStatus;
  }
  
  @override
  int get hashCode => toString().hashCode;
  
  @override
  String toString() {
    var buffer = new StringBuffer();
    
    buffer.write("UserInfo {");
    buffer.write("userId=" + "$_userId" + ", ");
    buffer.write("userFullName=" + "$_userFullName" + ", ");
    buffer.write("userWhatsAppNumber=" + "$_userWhatsAppNumber" + ", ");
    buffer.write("userStatus=" + (_userStatus != null ? amplify_core.enumToString(_userStatus)! : "null") + ", ");
    buffer.write("createdAt=" + (_createdAt != null ? _createdAt.format() : "null") + ", ");
    buffer.write("updatedAt=" + (_updatedAt != null ? _updatedAt.format() : "null"));
    buffer.write("}");
    
    return buffer.toString();
  }
  
  UserInfo copyWith({String? userFullName, String? userWhatsAppNumber, UserInfoUserStatus? userStatus}) {
    return UserInfo._internal(
      userId: userId,
      userFullName: userFullName ?? this.userFullName,
      userWhatsAppNumber: userWhatsAppNumber ?? this.userWhatsAppNumber,
      userStatus: userStatus ?? this.userStatus);
  }
  
  UserInfo copyWithModelFieldValues({
    ModelFieldValue<String>? userFullName,
    ModelFieldValue<String>? userWhatsAppNumber,
    ModelFieldValue<UserInfoUserStatus?>? userStatus
  }) {
    return UserInfo._internal(
      userId: userId,
      userFullName: userFullName == null ? this.userFullName : userFullName.value,
      userWhatsAppNumber: userWhatsAppNumber == null ? this.userWhatsAppNumber : userWhatsAppNumber.value,
      userStatus: userStatus == null ? this.userStatus : userStatus.value
    );
  }
  
  UserInfo.fromJson(Map<String, dynamic> json)  
    : _userId = json['userId'],
      _userFullName = json['userFullName'],
      _userWhatsAppNumber = json['userWhatsAppNumber'],
      _userStatus = amplify_core.enumFromString<UserInfoUserStatus>(json['userStatus'], UserInfoUserStatus.values),
      _createdAt = json['createdAt'] != null ? amplify_core.TemporalDateTime.fromString(json['createdAt']) : null,
      _updatedAt = json['updatedAt'] != null ? amplify_core.TemporalDateTime.fromString(json['updatedAt']) : null;
  
  Map<String, dynamic> toJson() => {
    'userId': _userId, 'userFullName': _userFullName, 'userWhatsAppNumber': _userWhatsAppNumber, 'userStatus': amplify_core.enumToString(_userStatus), 'createdAt': _createdAt?.format(), 'updatedAt': _updatedAt?.format()
  };
  
  Map<String, Object?> toMap() => {
    'userId': _userId,
    'userFullName': _userFullName,
    'userWhatsAppNumber': _userWhatsAppNumber,
    'userStatus': _userStatus,
    'createdAt': _createdAt,
    'updatedAt': _updatedAt
  };

  static final amplify_core.QueryModelIdentifier<UserInfoModelIdentifier> MODEL_IDENTIFIER = amplify_core.QueryModelIdentifier<UserInfoModelIdentifier>();
  static final USERID = amplify_core.QueryField(fieldName: "userId");
  static final USERFULLNAME = amplify_core.QueryField(fieldName: "userFullName");
  static final USERWHATSAPPNUMBER = amplify_core.QueryField(fieldName: "userWhatsAppNumber");
  static final USERSTATUS = amplify_core.QueryField(fieldName: "userStatus");
  static var schema = amplify_core.Model.defineSchema(define: (amplify_core.ModelSchemaDefinition modelSchemaDefinition) {
    modelSchemaDefinition.name = "UserInfo";
    modelSchemaDefinition.pluralName = "UserInfos";
    
    modelSchemaDefinition.authRules = [
      amplify_core.AuthRule(
        authStrategy: amplify_core.AuthStrategy.PUBLIC,
        provider: amplify_core.AuthRuleProvider.IAM,
        operations: const [
          amplify_core.ModelOperation.CREATE,
          amplify_core.ModelOperation.UPDATE,
          amplify_core.ModelOperation.DELETE,
          amplify_core.ModelOperation.READ
        ])
    ];
    
    modelSchemaDefinition.indexes = [
      amplify_core.ModelIndex(fields: const ["userId"], name: null)
    ];
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: UserInfo.USERID,
      isRequired: true,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: UserInfo.USERFULLNAME,
      isRequired: true,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: UserInfo.USERWHATSAPPNUMBER,
      isRequired: true,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: UserInfo.USERSTATUS,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.enumeration)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.nonQueryField(
      fieldName: 'createdAt',
      isRequired: false,
      isReadOnly: true,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.dateTime)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.nonQueryField(
      fieldName: 'updatedAt',
      isRequired: false,
      isReadOnly: true,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.dateTime)
    ));
  });
}

class _UserInfoModelType extends amplify_core.ModelType<UserInfo> {
  const _UserInfoModelType();
  
  @override
  UserInfo fromJson(Map<String, dynamic> jsonData) {
    return UserInfo.fromJson(jsonData);
  }
  
  @override
  String modelName() {
    return 'UserInfo';
  }
}

/**
 * This is an auto generated class representing the model identifier
 * of [UserInfo] in your schema.
 */
class UserInfoModelIdentifier implements amplify_core.ModelIdentifier<UserInfo> {
  final String userId;

  /** Create an instance of UserInfoModelIdentifier using [userId] the primary key. */
  const UserInfoModelIdentifier({
    required this.userId});
  
  @override
  Map<String, dynamic> serializeAsMap() => (<String, dynamic>{
    'userId': userId
  });
  
  @override
  List<Map<String, dynamic>> serializeAsList() => serializeAsMap()
    .entries
    .map((entry) => (<String, dynamic>{ entry.key: entry.value }))
    .toList();
  
  @override
  String serializeAsString() => serializeAsMap().values.join('#');
  
  @override
  String toString() => 'UserInfoModelIdentifier(userId: $userId)';
  
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) {
      return true;
    }
    
    return other is UserInfoModelIdentifier &&
      userId == other.userId;
  }
  
  @override
  int get hashCode =>
    userId.hashCode;
}