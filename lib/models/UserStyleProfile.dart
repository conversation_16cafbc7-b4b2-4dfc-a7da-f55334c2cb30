/*
* Copyright 2021 Amazon.com, Inc. or its affiliates. All Rights Reserved.
*
* Licensed under the Apache License, Version 2.0 (the "License").
* You may not use this file except in compliance with the License.
* A copy of the License is located at
*
*  http://aws.amazon.com/apache2.0
*
* or in the "license" file accompanying this file. This file is distributed
* on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either
* express or implied. See the License for the specific language governing
* permissions and limitations under the License.
*/

// NOTE: This file is generated and may not follow lint rules defined in your app
// Generated files can be excluded from analysis in analysis_options.yaml
// For more info, see: https://dart.dev/guides/language/analysis-options#excluding-code-from-analysis

// ignore_for_file: public_member_api_docs, annotate_overrides, dead_code, dead_codepublic_member_api_docs, depend_on_referenced_packages, file_names, library_private_types_in_public_api, no_leading_underscores_for_library_prefixes, no_leading_underscores_for_local_identifiers, non_constant_identifier_names, null_check_on_nullable_type_parameter, override_on_non_overriding_member, prefer_adjacent_string_concatenation, prefer_const_constructors, prefer_if_null_operators, prefer_interpolation_to_compose_strings, slash_for_doc_comments, sort_child_properties_last, unnecessary_const, unnecessary_constructor_name, unnecessary_late, unnecessary_new, unnecessary_null_aware_assignments, unnecessary_nullable_for_final_variable_declarations, unnecessary_string_interpolations, use_build_context_synchronously

import 'ModelProvider.dart';
import 'package:amplify_core/amplify_core.dart' as amplify_core;


/** This is an auto generated class representing the UserStyleProfile type in your schema. */
class UserStyleProfile extends amplify_core.Model {
  static const classType = const _UserStyleProfileModelType();
  final String? _userId;
  final UserStyleProfileUserBodyType? _userBodyType;
  final UserStyleProfileUserSkinUnderTone? _userSkinUnderTone;
  final UserStyleProfileUserAge? _userAge;
  final amplify_core.TemporalDateTime? _createdAt;
  final amplify_core.TemporalDateTime? _updatedAt;

  @override
  getInstanceType() => classType;
  
  @Deprecated('[getId] is being deprecated in favor of custom primary key feature. Use getter [modelIdentifier] to get model identifier.')
  @override
  String getId() => modelIdentifier.serializeAsString();
  
  UserStyleProfileModelIdentifier get modelIdentifier {
    try {
      return UserStyleProfileModelIdentifier(
        userId: _userId!
      );
    } catch(e) {
      throw amplify_core.AmplifyCodeGenModelException(
          amplify_core.AmplifyExceptionMessages.codeGenRequiredFieldForceCastExceptionMessage,
          recoverySuggestion:
            amplify_core.AmplifyExceptionMessages.codeGenRequiredFieldForceCastRecoverySuggestion,
          underlyingException: e.toString()
          );
    }
  }
  
  String get userId {
    try {
      return _userId!;
    } catch(e) {
      throw amplify_core.AmplifyCodeGenModelException(
          amplify_core.AmplifyExceptionMessages.codeGenRequiredFieldForceCastExceptionMessage,
          recoverySuggestion:
            amplify_core.AmplifyExceptionMessages.codeGenRequiredFieldForceCastRecoverySuggestion,
          underlyingException: e.toString()
          );
    }
  }
  
  UserStyleProfileUserBodyType? get userBodyType {
    return _userBodyType;
  }
  
  UserStyleProfileUserSkinUnderTone? get userSkinUnderTone {
    return _userSkinUnderTone;
  }
  
  UserStyleProfileUserAge? get userAge {
    return _userAge;
  }
  
  amplify_core.TemporalDateTime? get createdAt {
    return _createdAt;
  }
  
  amplify_core.TemporalDateTime? get updatedAt {
    return _updatedAt;
  }
  
  const UserStyleProfile._internal({required userId, userBodyType, userSkinUnderTone, userAge, createdAt, updatedAt}): _userId = userId, _userBodyType = userBodyType, _userSkinUnderTone = userSkinUnderTone, _userAge = userAge, _createdAt = createdAt, _updatedAt = updatedAt;
  
  factory UserStyleProfile({required String userId, UserStyleProfileUserBodyType? userBodyType, UserStyleProfileUserSkinUnderTone? userSkinUnderTone, UserStyleProfileUserAge? userAge}) {
    return UserStyleProfile._internal(
      userId: userId,
      userBodyType: userBodyType,
      userSkinUnderTone: userSkinUnderTone,
      userAge: userAge);
  }
  
  bool equals(Object other) {
    return this == other;
  }
  
  @override
  bool operator ==(Object other) {
    if (identical(other, this)) return true;
    return other is UserStyleProfile &&
      _userId == other._userId &&
      _userBodyType == other._userBodyType &&
      _userSkinUnderTone == other._userSkinUnderTone &&
      _userAge == other._userAge;
  }
  
  @override
  int get hashCode => toString().hashCode;
  
  @override
  String toString() {
    var buffer = new StringBuffer();
    
    buffer.write("UserStyleProfile {");
    buffer.write("userId=" + "$_userId" + ", ");
    buffer.write("userBodyType=" + (_userBodyType != null ? amplify_core.enumToString(_userBodyType)! : "null") + ", ");
    buffer.write("userSkinUnderTone=" + (_userSkinUnderTone != null ? amplify_core.enumToString(_userSkinUnderTone)! : "null") + ", ");
    buffer.write("userAge=" + (_userAge != null ? amplify_core.enumToString(_userAge)! : "null") + ", ");
    buffer.write("createdAt=" + (_createdAt != null ? _createdAt.format() : "null") + ", ");
    buffer.write("updatedAt=" + (_updatedAt != null ? _updatedAt.format() : "null"));
    buffer.write("}");
    
    return buffer.toString();
  }
  
  UserStyleProfile copyWith({UserStyleProfileUserBodyType? userBodyType, UserStyleProfileUserSkinUnderTone? userSkinUnderTone, UserStyleProfileUserAge? userAge}) {
    return UserStyleProfile._internal(
      userId: userId,
      userBodyType: userBodyType ?? this.userBodyType,
      userSkinUnderTone: userSkinUnderTone ?? this.userSkinUnderTone,
      userAge: userAge ?? this.userAge);
  }
  
  UserStyleProfile copyWithModelFieldValues({
    ModelFieldValue<UserStyleProfileUserBodyType?>? userBodyType,
    ModelFieldValue<UserStyleProfileUserSkinUnderTone?>? userSkinUnderTone,
    ModelFieldValue<UserStyleProfileUserAge?>? userAge
  }) {
    return UserStyleProfile._internal(
      userId: userId,
      userBodyType: userBodyType == null ? this.userBodyType : userBodyType.value,
      userSkinUnderTone: userSkinUnderTone == null ? this.userSkinUnderTone : userSkinUnderTone.value,
      userAge: userAge == null ? this.userAge : userAge.value
    );
  }
  
  UserStyleProfile.fromJson(Map<String, dynamic> json)  
    : _userId = json['userId'],
      _userBodyType = amplify_core.enumFromString<UserStyleProfileUserBodyType>(json['userBodyType'], UserStyleProfileUserBodyType.values),
      _userSkinUnderTone = amplify_core.enumFromString<UserStyleProfileUserSkinUnderTone>(json['userSkinUnderTone'], UserStyleProfileUserSkinUnderTone.values),
      _userAge = amplify_core.enumFromString<UserStyleProfileUserAge>(json['userAge'], UserStyleProfileUserAge.values),
      _createdAt = json['createdAt'] != null ? amplify_core.TemporalDateTime.fromString(json['createdAt']) : null,
      _updatedAt = json['updatedAt'] != null ? amplify_core.TemporalDateTime.fromString(json['updatedAt']) : null;
  
  Map<String, dynamic> toJson() => {
    'userId': _userId, 'userBodyType': amplify_core.enumToString(_userBodyType), 'userSkinUnderTone': amplify_core.enumToString(_userSkinUnderTone), 'userAge': amplify_core.enumToString(_userAge), 'createdAt': _createdAt?.format(), 'updatedAt': _updatedAt?.format()
  };
  
  Map<String, Object?> toMap() => {
    'userId': _userId,
    'userBodyType': _userBodyType,
    'userSkinUnderTone': _userSkinUnderTone,
    'userAge': _userAge,
    'createdAt': _createdAt,
    'updatedAt': _updatedAt
  };

  static final amplify_core.QueryModelIdentifier<UserStyleProfileModelIdentifier> MODEL_IDENTIFIER = amplify_core.QueryModelIdentifier<UserStyleProfileModelIdentifier>();
  static final USERID = amplify_core.QueryField(fieldName: "userId");
  static final USERBODYTYPE = amplify_core.QueryField(fieldName: "userBodyType");
  static final USERSKINUNDERTONE = amplify_core.QueryField(fieldName: "userSkinUnderTone");
  static final USERAGE = amplify_core.QueryField(fieldName: "userAge");
  static var schema = amplify_core.Model.defineSchema(define: (amplify_core.ModelSchemaDefinition modelSchemaDefinition) {
    modelSchemaDefinition.name = "UserStyleProfile";
    modelSchemaDefinition.pluralName = "UserStyleProfiles";
    
    modelSchemaDefinition.authRules = [
      amplify_core.AuthRule(
        authStrategy: amplify_core.AuthStrategy.PUBLIC,
        provider: amplify_core.AuthRuleProvider.IAM,
        operations: const [
          amplify_core.ModelOperation.CREATE,
          amplify_core.ModelOperation.UPDATE,
          amplify_core.ModelOperation.DELETE,
          amplify_core.ModelOperation.READ
        ])
    ];
    
    modelSchemaDefinition.indexes = [
      amplify_core.ModelIndex(fields: const ["userId"], name: null)
    ];
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: UserStyleProfile.USERID,
      isRequired: true,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.string)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: UserStyleProfile.USERBODYTYPE,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.enumeration)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: UserStyleProfile.USERSKINUNDERTONE,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.enumeration)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.field(
      key: UserStyleProfile.USERAGE,
      isRequired: false,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.enumeration)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.nonQueryField(
      fieldName: 'createdAt',
      isRequired: false,
      isReadOnly: true,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.dateTime)
    ));
    
    modelSchemaDefinition.addField(amplify_core.ModelFieldDefinition.nonQueryField(
      fieldName: 'updatedAt',
      isRequired: false,
      isReadOnly: true,
      ofType: amplify_core.ModelFieldType(amplify_core.ModelFieldTypeEnum.dateTime)
    ));
  });
}

class _UserStyleProfileModelType extends amplify_core.ModelType<UserStyleProfile> {
  const _UserStyleProfileModelType();
  
  @override
  UserStyleProfile fromJson(Map<String, dynamic> jsonData) {
    return UserStyleProfile.fromJson(jsonData);
  }
  
  @override
  String modelName() {
    return 'UserStyleProfile';
  }
}

/**
 * This is an auto generated class representing the model identifier
 * of [UserStyleProfile] in your schema.
 */
class UserStyleProfileModelIdentifier implements amplify_core.ModelIdentifier<UserStyleProfile> {
  final String userId;

  /** Create an instance of UserStyleProfileModelIdentifier using [userId] the primary key. */
  const UserStyleProfileModelIdentifier({
    required this.userId});
  
  @override
  Map<String, dynamic> serializeAsMap() => (<String, dynamic>{
    'userId': userId
  });
  
  @override
  List<Map<String, dynamic>> serializeAsList() => serializeAsMap()
    .entries
    .map((entry) => (<String, dynamic>{ entry.key: entry.value }))
    .toList();
  
  @override
  String serializeAsString() => serializeAsMap().values.join('#');
  
  @override
  String toString() => 'UserStyleProfileModelIdentifier(userId: $userId)';
  
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) {
      return true;
    }
    
    return other is UserStyleProfileModelIdentifier &&
      userId == other.userId;
  }
  
  @override
  int get hashCode =>
    userId.hashCode;
}